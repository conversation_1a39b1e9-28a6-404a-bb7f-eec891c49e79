# 需求视图操作按钮功能说明

## 功能概述

在需求视图中实现了智能的操作按钮显示与功能逻辑，根据用例的不同状态动态控制按钮的可用性，并新增了忽略用例和还原用例的功能。

## 用例状态定义

- **0**: 未上传
- **1**: 待Review  
- **2**: Review中
- **3**: 已Review
- **4**: 已修订
- **5**: 已忽略

## 操作按钮逻辑

### 1. 发起Review按钮
- **显示条件**: 用例状态不为"未上传"(caseStatus !== 0)
- **禁用条件**: 用例状态为"未上传"时置灰禁用
- **禁用提示**: "用例未上传，无法发起Review"
- **忽略状态**: 被忽略的用例不显示此按钮

### 2. 上传用例按钮
- **显示条件**: 所有正常状态都显示
- **功能**: 目前显示提示信息，待后续实现具体逻辑
- **忽略状态**: 被忽略的用例不显示此按钮

### 3. 忽略按钮
- **显示条件**: 用例状态为"未上传"或"待Review"(caseStatus === 0 || caseStatus === 1)
- **禁用条件**: 其他状态时置灰禁用
- **禁用提示**: "当前状态无法忽略"
- **功能**: 点击后弹出忽略原因填写弹窗

### 4. 还原按钮
- **显示条件**: 仅在用例被忽略时显示(isIgnored === true)
- **功能**: 将忽略的用例还原为"未上传"状态
- **位置**: 替换发起Review按钮的位置

## 忽略功能详细说明

### 忽略流程
1. 用户点击"忽略"按钮
2. 弹出忽略原因填写弹窗
3. 用户必须填写忽略原因（必填项，最多500字符）
4. 确认后用例状态变更为"已忽略"
5. 行样式变为灰色半透明，表示已忽略

### 忽略原因弹窗
- **标题**: "忽略用例"
- **内容**: 显示用例名称、需求单信息
- **表单**: 忽略原因文本域（必填，最多500字符）
- **按钮**: "确认忽略"、"取消"

### 还原功能
- 点击还原按钮可将忽略的用例恢复为正常状态
- 还原后用例状态变为"未上传"
- 清除忽略原因和忽略标记
- 行样式恢复正常

## 视觉效果

### 忽略行样式
- **背景色**: 浅红色 (#fff2f0)
- **透明度**: 0.7
- **悬停效果**: 更深的浅红色 (#ffebe6)
- **文字颜色**: 灰色 (#999)

### 禁用按钮样式
- **透明度**: 0.4
- **鼠标样式**: not-allowed
- **交互**: 禁用点击事件

### 状态显示
- **已忽略状态**: 红色文字 (#f5222d)
- **信息图标**: 悬停显示忽略原因的Tooltip

## 数据结构

### 用例记录新增字段
```javascript
{
  id: 2218,
  title: '商品详情页测试用例',
  caseStatus: 5, // 已忽略
  isIgnored: true, // 是否被忽略
  ignoreReason: '需求变更，该功能暂时不开发', // 忽略原因
  // ... 其他字段
}
```

## 技术实现

### 组件文件
1. **IgnoreReasonModal.jsx**: 忽略原因填写弹窗组件
2. **list.js**: 主要逻辑实现，包含按钮渲染和状态管理
3. **mockData.js**: 添加忽略相关的Mock数据
4. **index.scss**: 忽略行和禁用按钮的样式

### 核心方法
- `renderRequirementViewActions()`: 根据状态渲染不同的操作按钮
- `handleIgnoreCase()`: 显示忽略原因弹窗
- `handleIgnoreConfirm()`: 确认忽略用例
- `handleRestoreIgnored()`: 还原忽略的用例

### 状态管理
```javascript
state = {
  ignoreReasonVisible: false, // 忽略原因弹窗显示状态
  ignoreRecord: null, // 要忽略的记录
  // ... 其他状态
}
```

## 使用说明

### 忽略用例
1. 在需求视图中找到状态为"未上传"或"待Review"的用例
2. 点击操作栏中的"忽略"按钮（眼睛图标）
3. 在弹窗中填写详细的忽略原因
4. 点击"确认忽略"完成操作

### 还原用例
1. 找到已忽略的用例（行背景为浅红色）
2. 点击操作栏中的"还原"按钮（撤销图标）
3. 确认后用例恢复为正常状态

### 查看忽略原因
1. 在用例状态列中，已忽略的用例会显示信息图标
2. 鼠标悬停在信息图标上可查看忽略原因

## 后续开发建议

1. **接口对接**: 将Mock数据替换为真实的后端接口调用
2. **权限控制**: 添加用户权限验证，控制谁可以忽略/还原用例
3. **操作日志**: 记录忽略和还原操作的历史记录
4. **批量操作**: 支持批量忽略和还原功能
5. **通知机制**: 忽略用例时通知相关人员

## 测试要点

1. 验证不同状态下按钮的显示/禁用逻辑
2. 测试忽略原因弹窗的表单验证
3. 验证忽略和还原功能的数据更新
4. 检查忽略行的视觉效果
5. 测试忽略原因的显示功能
