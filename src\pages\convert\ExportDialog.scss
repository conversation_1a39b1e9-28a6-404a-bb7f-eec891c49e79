.export-dialog {
  .ant-modal-header {
    border-bottom: 1px solid #e8e8e8;
    padding: 16px 24px;

    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-footer {
    border-top: 1px solid #e8e8e8;
    padding: 12px 24px;
    text-align: right;

    .ant-btn {
      margin-left: 8px;
      
      &:first-child {
        margin-left: 0;
      }
    }
  }
}

.export-content {
  .file-info-section,
  .export-format-section,
  .export-preview-section {
    margin-bottom: 16px;

    h4 {
      font-size: 14px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 12px;
      margin-top: 0;
    }
  }

  /* 文件信息区域 */
  .file-info-section {
    .file-info {
      .file-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: #fafafa;
        border-radius: 6px;
        border: 1px solid #e8e8e8;

        .file-icon {
          font-size: 24px;
          color: #1890ff;
        }

        .file-details {
          flex: 1;

          .file-name {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
            margin-bottom: 4px;
          }

          .file-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #8c8c8c;

            span {
              position: relative;

              &:not(:last-child)::after {
                content: '|';
                position: absolute;
                right: -6px;
                color: #d9d9d9;
              }
            }
          }
        }
      }
    }

    .no-file {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background: #fff7e6;
      border-radius: 6px;
      border: 1px solid #ffd591;
      color: #d46b08;
      font-size: 14px;
    }
  }

  /* 导出格式选择区域 */
  .export-format-section {
    .format-radio-group {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .ant-radio-button-wrapper {
        height: auto;
        border-radius: 6px;
        border: 1px solid #e8e8e8;
        background: #fafafa;
        padding: 0;
        text-align: left;
        transition: all 0.2s ease;

        &:hover {
          border-color: #40a9ff;
          background: #f0f9ff;
        }

        &:first-child {
          border-radius: 6px;
        }

        &:last-child {
          border-radius: 6px;
        }

        &:not(:first-child) {
          border-left: 1px solid #e8e8e8;
        }

        &.ant-radio-button-wrapper-checked {
          border-color: #1890ff;
          background: #e6f7ff;
          color: #1890ff;
          z-index: 1;
        }

        .format-content {
          padding: 16px;

          .format-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;

            .format-icon {
              font-size: 18px;
            }

            .format-label {
              font-size: 14px;
              font-weight: 500;
              color: #262626;
            }

            .format-extension {
              margin-left: auto;
              font-size: 12px;
              color: #8c8c8c;
              background: #f0f0f0;
              padding: 2px 6px;
              border-radius: 4px;
              font-family: 'Consolas', 'Monaco', monospace;
            }
          }

          .format-description {
            font-size: 12px;
            color: #8c8c8c;
            line-height: 1.5;
            margin-left: 26px;
          }
        }

        &.ant-radio-button-wrapper-checked .format-content {
          .format-label {
            color: #1890ff;
          }

          .format-extension {
            background: #bae7ff;
            color: #0050b3;
          }
        }
      }
    }
  }

  /* 导出预览区域 */
  .export-preview-section {
    .preview-info {
      background: #f8f9fa;
      border-radius: 6px;
      padding: 16px;
      border: 1px solid #e9ecef;

      .preview-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .preview-label {
          font-size: 13px;
          color: #666;
          width: 80px;
          flex-shrink: 0;
        }

        .preview-value {
          font-size: 13px;
          color: #262626;
          font-weight: 500;
        }
      }
    }
  }

  /* 分隔线样式 */
  .ant-divider {
    margin: 20px 0;
    border-color: #e8e8e8;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .export-dialog {
    .ant-modal {
      width: 90% !important;
      max-width: 400px;
    }

    .ant-modal-body {
      padding: 16px;
    }

    .export-content {
      .file-info-section .file-info .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .file-icon {
          align-self: center;
        }
      }

      .export-format-section .format-radio-group {
        .ant-radio-button-wrapper .format-content {
          padding: 12px;

          .format-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;

            .format-extension {
              margin-left: 0;
              align-self: flex-end;
            }
          }

          .format-description {
            margin-left: 0;
            font-size: 11px;
          }
        }
      }

      .export-preview-section .preview-info {
        padding: 12px;

        .preview-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;

          .preview-label {
            width: auto;
            font-size: 12px;
          }

          .preview-value {
            font-size: 12px;
          }
        }
      }
    }
  }
}

/* 导出中状态样式 */
.export-dialog.exporting {
  .ant-modal-body {
    pointer-events: none;
    opacity: 0.7;
  }

  .format-radio-group {
    pointer-events: none;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.export-content {
  animation: fadeIn 0.3s ease-out;
}

/* 按钮样式增强 */
.ant-modal-footer {
  .ant-btn {
    height: 36px;
    padding: 0 16px;
    font-size: 14px;
    border-radius: 4px;

    &.ant-btn-primary {
      box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);

      &:hover {
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
      }
    }
  }
} 