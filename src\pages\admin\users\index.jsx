// import React from 'react';
import Headers from '../../../layouts/header';
import React, { useState, useEffect } from 'react';
import { Table, Button, Divider, Tag, message, Input, Modal, Card } from 'antd';
import UserFormModal from './components/UserFormMoal'; // 弹簧组件
import request from '@/utils/axios'; // 导入封装的request
import PasswordModal from './components/PasswordModal'; // 修改密码弹窗
const UserManagement = () => {
  const isOffline = process.env.APP_VERSION === 'offline';
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  // 搜索表单
  const [searchForm, setSearchForm] = useState({
    username: '',
    phone: '',
  });
  // 修改密码弹窗状态
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [passwordConfirmLoading, setPasswordConfirmLoading] = useState(false);

  // 弹框组件字段
  const [modalVisible, setModalVisible] = useState(false);
  const [editData, setEditData] = useState(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  // 处理表单提交
  const handleFormSubmit = async values => {
    setConfirmLoading(true);
    try {
      let response;
      if (editData) {
        // 编辑用户
        response = await request(`/user/update`, {
          method: 'POST',
          body: {
            id: editData.id,
            username: values.username,
            email: values.email,
            phone: values.phone,
            isDelete: values.status,
            // last_name: values.realName,
            // password: values.password,
            // is_superuser: values.role === '系统管理员' ? 1 : 0,
            // is_active: values.status ? 1 : 0,
            // biz: values.projects.map(id => id),
            company: values.role === 'ROLE_ADMIN' ? [] : values.projects.map(id => id),
            authorityName: values.role,
            // visCompany:values.projects.map(id => id)
          },
        });
      } else {
        // 新增用户
        // response = await request('/user/', {
        //   method: 'POST',
        //   body: {
        //     username: values.username,
        //     email: values.email,
        //     phone: values.phone,
        //     last_name: values.realName,
        //     password: values.password,
        //     is_superuser: values.role === '系统管理员' ? 1 : 0,
        //     is_active: values.status ? 1 : 0,
        //     biz: values.projects.map(id => id),
        //   },
        // });
      }

      if (response && response.code === 200) {
        message.success(`${editData ? '编辑' : '新增'}用户成功`);
        setModalVisible(false);
        setEditData(null);
        fetchUserData(); // 刷新表格数据
      } else {
        message.error(response?.msg || `${editData ? '编辑' : '新增'}用户失败`);
      }
    } catch (error) {
      console.error('操作失败:', error);
      message.error(`${editData ? '编辑' : '新增'}用户失败`);
    }
    setConfirmLoading(false);
  };

  // 获取数据的API调用
  const fetchUserData = async (params = {}) => {
    setLoading(true);

    try {
      const res = await request('user/queryByPage', {
        method: 'GET',
        params: {
          pageNum: params.current || pagination.current,
          pageSize: params.pageSize || pagination.pageSize,
          username: params.username || searchForm.username,
          phone: params.phone || searchForm.phone,
        },
      });
      // const res = mockData;

      if (res && res.code === 200) {
        const arr = [];
        res.data.dataSources?.map((item, index) => {
          arr.push({
            id: item.id,
            username: item.username,
            phone: item.phone,
            // realName: item.last_name,
            // role: item.is_superuser ? '系统管理员' : '普通用户',
            role: item.authorityDesc,
            projects:item.company ? item.company?.map(item => item) : [],
            // projectsId: item.biz_info?.map(item => item.id),
            email: item.email,
            createTime: item.gmtCreated,
            status: item.isDeleteName,
            companyId: item.companyId ? item.companyId : [],
            isDelete: item.isDelete,
            authorityName: item.authorityName,
          });
        });
        res.resetData = arr;
        setUserData(res.resetData);
        setPagination(prev => ({
          ...prev,
          current: params.current || pagination.current,
          total: res.data.total,
        }));
      }else{
        message.error(res?.msg || '获取数据失败');
      }
    } catch (error) {
      message.error('获取用户数据失败');
    }
    setLoading(false);
  };

  // 修改 useEffect，添加对 searchForm 的监听
  useEffect(() => {
    fetchUserData({
      current: 1,
      ...searchForm,
    });
  }, [searchForm]); // 添加 searchForm 作为依赖

  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    fetchUserData({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm,
      ...filters,
    });
  };
  // 搜索栏布局样式
  const searchBarStyle = {
    background: '#fff',
    padding: '24px',
    marginBottom: '16px',
    borderRadius: '4px',
  };

  // 表格区域样式
  const tableAreaStyle = {
    background: '#fff',
    padding: '24px',
    borderRadius: '4px',
    height: '800px',
    overflow: 'hidden',
  };

  // 搜索表单项样式
  const searchItemStyle = {
    width: '240px',
    marginRight: '16px',
    display: 'inline-block',
  };

  // 按钮样式
  const buttonStyle = {
    marginRight: '8px',
  };
  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
    },
    // {
    //   title: '姓名',
    //   dataIndex: 'realName',
    //   key: 'realName',
    //   width: 100,
    // },
    {
      title: '系统角色',
      dataIndex: 'role',
      key: 'role',
    },
    {
      title: '所属项目',
      dataIndex: 'projects',
      key: 'projects',
      // render: projects => (
      //   <span>
      //     {projects?.map((project, index) => (
      //       <Tag color="blue" key={index}>
      //         {project}
      //       </Tag>
      //     ))}
      //   </span>
      // ),
      render: (projects, record) => {
        const isAdmin = record.role === '管理员' || record.authorityName === 'ROLE_ADMIN';        
        if (isAdmin) {
          return (
            <Tag color="gold" style={{ fontWeight: 'bold' }}>
              <span style={{ fontSize: '14px' }}>全部项目</span>
            </Tag>
          );
        }       
        return (
          <span>
            {projects?.length ? (
              projects.map((project, index) => (
                <Tag 
                  color="blue" 
                  key={index}
                  style={{ 
                    marginBottom: '4px',
                    display: 'inline-block'
                  }}
                >
                  {project}
                </Tag>
              ))
            ) : (
              <span style={{ color: '#999' }}>暂无项目</span>
            )}
          </span>
        );
      },
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      // render: status => (
      //   <Tag color={status === 1 ? 'green' : 'red'}>
      //     {status === 1 ? '正常' : '停用'}
      //   </Tag>
      // ),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      render: (_, record) => {
        // 添加判断条件：离线版本且用户名为admin时禁用所有按钮
        const isDisabled = isOffline && record.username === 'admin';       
        return (
          <div>
            <Button
              type="link"
              onClick={() => handleEdit(record)}
              style={{
                marginRight: '4px',
                padding: '4px 8px',
              }}
              disabled={isDisabled}
            >
              编辑
            </Button>
            {isOffline && (
              <Button
                type="link"
                style={{
                  marginRight: '4px',
                  padding: '4px 8px',
                }}
                onClick={() => handleResetPassword(record)}
                disabled={isDisabled}
              >
                重置密码
              </Button>
            )}
            <Button
              type="link"
              style={{
                color: '#ff4d4f',
                padding: '4px 8px',
              }}
              onClick={() => handleDelete(record)}
              disabled={isDisabled}
            >
              删除
            </Button>
          </div>
        );
      },
    }
  ];

  // 打开新增用户弹窗
  // const handleAdd = () => {
  //   setEditData(null);
  //   setModalVisible(true);
  // };

  // 打开编辑用户弹窗
  const handleEdit = record => {
    setEditData(record);
    setModalVisible(true);
  };

  // 处理删除
  const handleDelete = record => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除用户 "${record.username}" 吗？`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 这里应该调用实际的删除API
          // await request.delete(`/api/users/${record.id}`);
          const res = await request(`/user/delete`, {
            method: 'POST',
            body: {
              id: record.id,
            },
          });
          if (res && res.code === 200) {
            message.success('删除成功');
            fetchUserData(); // 刷新表格数据
          } else {
            message.error(res?.msg || '删除失败');
          }
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 打开修改密码弹窗
  const handleResetPassword = record => {
    setSelectedUser(record);
    setPasswordModalVisible(true);
  };
  // 处理密码修改提交
  const handlePasswordSubmit = async password => {
    setPasswordConfirmLoading(true);
    try {
      const response = await request(`/user/resetPwd`, {
        method: 'POST',
        body: {
          ids: [selectedUser.id],
          // password: password,
        },
      });

      if (response && response.code === 200) {
        message.success('密码重置成功!');
        setPasswordModalVisible(false);
        setSelectedUser(null);
      } else {
        message.error(response?.msg || '密码重置失败');
      }
    } catch (error) {
      console.error('密码重置失败:', error);
      message.error('密码重置失败');
    }
    setPasswordConfirmLoading(false);
  };
  const handleReset = () => {
    setSearchForm({
      username: '',
      phone: '',
    });
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
    fetchUserData({
      current: 1,
      username: '',
      phone: '',
    });
  };
  return (
    <section style={{ marginBottom: 30 }}>
      {/* <Headers /> */}
      <div style={{ padding: '24px', background: '#f0f2f5',paddingBottom: '0px' }}>
        {/* 搜索区域 */}
        <div style={searchBarStyle}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Input
              placeholder="用户名"
              style={searchItemStyle}
              value={searchForm.username}
              onChange={e =>
                setSearchForm(prev => ({ ...prev, username: e.target.value }))
              }
              allowClear
            />
            <Input
              placeholder="手机号"
              style={searchItemStyle}
              value={searchForm.phone}
              onChange={e =>
                setSearchForm(prev => ({ ...prev, phone: e.target.value }))
              }
              allowClear
            />
            {/* 移除查询按钮，保留重置按钮 */}
            <Button onClick={handleReset} style={buttonStyle}>
              重置
            </Button>
          </div>
        </div>

        {/* 表格区域 */}
        <div style={tableAreaStyle}>
          {/* <div
            style={{
              marginBottom: '16px',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <div>
              <Button
                type="primary"
                onClick={() => {
                  setEditData(null);
                  setModalVisible(true);
                }}
                style={buttonStyle}
              >
                + 新增人员
              </Button>
            </div>
            <div>
              <Button onClick={() => fetchUserData()} style={buttonStyle}>
                刷新
              </Button>
            </div>
          </div> */}

          <Table
            columns={columns}
            dataSource={userData}
            rowKey="id"
            pagination={{
              ...pagination,
              showQuickJumper: true,
              showSizeChanger: true,
              showTotal: total => `共 ${total} 条`,
            }}
            loading={loading}
            onChange={handleTableChange}
            //   scroll={{ x: 1500 }}
            // size="middle"
            //   rowSelection={{
            //     type: 'checkbox',
            //     onChange: (selectedRowKeys, selectedRows) => {
            //       console.log(selectedRowKeys, selectedRows);
            //     },
            //   }}
            scroll={{ y: 550 }} // 直接设置表格的滚动高度为600px
            style={{ height: 550 }} // 设置表格整体高度
          />
        </div>
        <UserFormModal
          visible={modalVisible}
          editData={editData}
          confirmLoading={confirmLoading}
          onCancel={() => {
            setModalVisible(false);
            setEditData(null);
          }}
          onOk={handleFormSubmit}
        />
      </div>
      <PasswordModal
        visible={passwordModalVisible}
        onCancel={() => {
          setPasswordModalVisible(false);
          setSelectedUser(null);
        }}
        onOk={handlePasswordSubmit}
        confirmLoading={passwordConfirmLoading}
        username={selectedUser?.username}
      />
    </section>
  );
};
export default UserManagement;
