.mindmap-viewer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;

  .mindmap-toolbar {
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .mindmap-title {
        font-size: 14px;
        font-weight: 600;
        color: #262626;
      }

      .file-info {
        font-size: 12px;
        color: #8c8c8c;
        background: #f0f0f0;
        padding: 2px 8px;
        border-radius: 4px;
      }
    }

    .toolbar-right {
      display: flex;
      gap: 8px;

      .ant-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        height: 28px;
        padding: 0 8px;
        border-radius: 4px;
        
        &:hover {
          background: #e6f7ff;
          border-color: #91d5ff;
        }
      }
    }
  }

  .mindmap-container {
    flex: 1;
    position: relative;
    background: #fafafa;
    overflow: hidden;

    svg {
      width: 100%;
      height: 100%;
      cursor: grab;
      
      &:active {
        cursor: grabbing;
      }
    }

    /* 加载状态 */
    .loading-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      z-index: 10;

      .ant-spin {
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: #666;
      }
    }

    /* 错误状态 */
    .error-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80%;
      max-width: 400px;
      z-index: 10;

      .ant-alert {
        border-radius: 8px;
      }
    }

    /* 空状态 */
    .empty-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      z-index: 10;

      .anticon {
        margin-bottom: 16px;
        display: block;
      }

      p {
        margin: 8px 0;
        font-size: 14px;
        color: #666;

        &.sub-text {
          font-size: 12px;
          color: #999;
          margin: 0;
        }
      }
    }
  }
}

/* 思维导图节点样式 */
.mindmap-node {
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    filter: brightness(1.1);
  }

  &.selected {
    stroke: #1890ff;
    stroke-width: 2;
  }
}

/* 思维导图连线样式 */
.mindmap-line {
  transition: all 0.2s ease;
  
  &:hover {
    stroke-width: 3;
    opacity: 0.8;
  }
}

/* 全屏模式样式 */
.mindmap-viewer:-webkit-full-screen {
  background: white;
  
  .mindmap-container {
    border-radius: 0;
  }
}

.mindmap-viewer:-moz-full-screen {
  background: white;
  
  .mindmap-container {
    border-radius: 0;
  }
}

.mindmap-viewer:fullscreen {
  background: white;
  
  .mindmap-container {
    border-radius: 0;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .mindmap-viewer {
    .mindmap-toolbar {
      padding: 8px 12px;
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;

      .toolbar-left {
        width: 100%;
        justify-content: space-between;
      }

      .toolbar-right {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .mindmap-container {
      .error-container {
        width: 90%;
        max-width: none;
      }
    }
  }
}

/* 滚动条样式 */
.mindmap-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.mindmap-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.mindmap-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;

  &:hover {
    background: #a8a8a8;
  }
}

/* 思维导图动画效果 */
@keyframes mindmapFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.mindmap-container svg {
  animation: mindmapFadeIn 0.5s ease-out;
}

/* 主题颜色变量 */
:root {
  --mindmap-primary-color: #1890ff;
  --mindmap-success-color: #52c41a;
  --mindmap-warning-color: #faad14;
  --mindmap-error-color: #ff4d4f;
  --mindmap-info-color: #722ed1;
} 