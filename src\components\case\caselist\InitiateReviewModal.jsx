import React from 'react';
import { Modal, Form, Select, DatePicker, Input, message, TreeSelect } from 'antd';
import moment from 'moment';
import request from '@/utils/axios';
import locale from 'antd/es/date-picker/locale/zh_CN';

const { TextArea } = Input;
const FormItem = Form.Item;

class InitiateReviewModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      userList: [],
      treeData: [],
      loading: false,
    };
  }

  componentDidMount() {
    this.getUserList();
    this.getReviewCaseList();
  }

  // 获取用户列表
  getUserList = () => {
    this.setState({ loading: true });
    
    request(`/user/companyUserList`, {
      method: 'GET',
      params: {
        id: this.props.productLineId
      }
    })
    .then(res => {
      if (res.code === 200) {
        const userList = (res.data || []).map(user => ({
          label: user.username,
          value: user.id,
          key: user.id
        }));
        
        this.setState({ 
          userList,
          loading: false
        });
      } else {
        console.error('获取用户列表失败:', res);
        message.error('获取用户列表失败: ' + (res.msg || '未知错误'));
        this.setState({ loading: false });
      }
    })
    .catch(err => {
      console.error('获取用户列表出错:', err);
      message.error('获取用户列表失败，请检查网络连接');
      this.setState({ loading: false });
    });
  };

  // 将数据转换为树形结构
  convertToTreeData = (data) => {
    const convertNode = (node) => {
      const result = {
        title: node.text,
        value: node.id,
        key: node.id,
      };

      // 如果有用例列表，添加用例作为叶子节点
      if (node.caseDtoList && node.caseDtoList.length > 0) {
        if (!result.children) {
          result.children = [];
        }
        node.caseDtoList.forEach(caseItem => {
          result.children.push({
            title: caseItem.title,
            value: caseItem.id.toString(),
            key: caseItem.id.toString(),
            isLeaf: true
          });
        });
      }

      // 如果有子文件夹，递归转换
      if (node.children && node.children.length > 0) {
        if (!result.children) {
          result.children = [];
        }
        result.children.push(...node.children.map(child => convertNode(child)));
      }

      // 如果没有children或children为空数组，删除children属性
      if (!result.children || result.children.length === 0) {
        delete result.children;
      }

      return result;
    };

    return [convertNode(data)];
  };

  getReviewCaseList = () => {
    const { productLineId } = this.props;
    request(`/case/reviewCaseList`, {
      method: 'GET',
      params: {
        productLineId,
        channel: 1,
        companyId: productLineId,
      }
    })
    .then(res => {
      if (res.code === 200) {
        const treeData = this.convertToTreeData(res.data);
        this.setState({ treeData });
      } else {
        console.error('获取用例列表失败:', res);
        message.error('获取用例列表失败: ' + (res.msg || '未知错误'));
      }
    })
    .catch(err => {
      console.error('获取用例列表出错:', err);
      message.error('获取用例列表失败，请检查网络连接');
    }); 
  };

  handleOk = () => {
    const { form, onOk, record } = this.props;
    form.validateFields((err, values) => {
      if (err) return;

      // 转换日期格式
      const data = {
        ...values,
        cutOffDate: values.cutOffDate
          ? values.cutOffDate.format('YYYY-MM-DD HH:mm:ss')
          : null,
        caseId: record.id, // 使用当前用例ID
      };

      onOk(data);
    });
  };

  disabledDate = current => {
    // 禁用今天之前的日期
    return current && current < moment().startOf('day');
  };

  render() {
    const { visible, onCancel, form, title, record } = this.props;
    const { userList, loading, treeData } = this.state;
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 19 },
    };

    // // 从record中获取创建人信息
    // const creator = record?.creator;
    // const creatorId = 698;

    return (
      <Modal
        title={title || "发起Review"}
        visible={visible}
        onOk={this.handleOk}
        onCancel={onCancel}
        width={600}
        okText="确定"
        cancelText="取消"
        maskClosable={false}
      >
        <Form {...formItemLayout}>
          <FormItem label="Review人" required>
            {getFieldDecorator('reviewId', {
              rules: [{ required: true, message: '请选择Review人' }],
            })(
              <Select
                mode="multiple"
                placeholder="请选择Review人"
                style={{ width: '100%' }}
                loading={loading}
                optionFilterProp="label"
                showSearch
              >
                {userList.map(user => (
                  <Select.Option key={user.key} value={user.value}>
                    {user.label}
                  </Select.Option>
                ))}
              </Select>
            )}
          </FormItem>

          <FormItem label="修订人" required>
            {getFieldDecorator('reviseId', {
              // initialValue: creatorId ? [creatorId] : [], // 默认设置为创建人
              rules: [{ required: true, message: '请选择修订人' }],
            })(
              <Select
                mode="multiple"
                placeholder="请选择修订人"
                style={{ width: '100%' }}
                loading={loading}
                optionFilterProp="label"
                showSearch
              >
                {userList.map(user => (
                  <Select.Option key={user.key} value={user.value}>
                    {user.label}
                  </Select.Option>
                ))}
              </Select>
            )}
          </FormItem>

          <FormItem label="测试用例" required>
            {getFieldDecorator('caseId', {
              initialValue: record?.id?.toString(),
              rules: [{ required: true, message: '请选择测试用例' }],
            })(
              <TreeSelect
                placeholder="请选择测试用例"
                style={{ width: '100%' }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                treeData={treeData}
                treeDefaultExpandAll
                showSearch
                disabled={true}
                treeNodeFilterProp="title"
              />
            )}
          </FormItem>

          <FormItem label="截止时间">
            {getFieldDecorator('cutOffDate')(
              <DatePicker
                style={{ width: '100%' }}
                disabledDate={this.disabledDate}
                placeholder="请选择截止时间"
                showTime={{ format: 'HH:mm' }}
                format="YYYY-MM-DD HH:mm"
                locale={locale}
                showToday={false}
              />
            )}
            <div style={{ color: 'red', fontSize: '12px', lineHeight: '12px' }}>
              截止时间非必选，不选则永不过期
            </div>
          </FormItem>

          <FormItem label="备注">
            {getFieldDecorator('note')(
              <TextArea
                placeholder="请输入备注信息"
                autoSize={{ minRows: 4, maxRows: 6 }}
              />,
            )}
          </FormItem>
        </Form>
      </Modal>
    );
  }
}

export default Form.create()(InitiateReviewModal); 