import React from 'react';
import { Layout, Menu, Icon, Popover, Badge } from 'antd';
import { router } from 'umi';
import styles from './index.scss';
import userInfoIcon from '../../images/userInfo.png';
const { Sider, Content } = Layout;
import PasswordModal from '../../components/user/PasswordModal';
import Cookies from 'js-cookie';
import request from '@/utils/axios';
class BasicLayout extends React.Component {
  state = {
    passwordModalVisible: false,
    isReview: false
  };
  componentDidMount() {
    // 离线版本时，检查登录状态
    if (process.env.APP_VERSION === 'offline') {
      const username = Cookies.get('username');
      if (!username) {
        // 清除会话信息
        sessionStorage.clear();
        Cookies.remove('username');
        Cookies.remove('userName');
        // 获取当前路径用于重定向
        const currentPath = window.location.hash.slice(1);
        const redirectUrl = encodeURIComponent(currentPath);
        // 跳转到登录页
        router.push(`/login?jumpto=${redirectUrl}`);
      }
    }
    
    // 在组件挂载时获取review内容
    this.getReviewContent();

    // 添加路由变化监听
    window.addEventListener('hashchange', this.handleRouteChange);
  }

  componentWillUnmount() {
    // 清理路由监听
    window.removeEventListener('hashchange', this.handleRouteChange);
  }

  handleRouteChange = () => {
    // 路由变化时重新获取review状态
    this.getReviewContent();
  };

  // 获取当前项目ID
  getCurrentProjectId = () => {   
    // 如果当前在首页，直接返回null
    const currentPath = window.location.hash.slice(1);
    if (
      currentPath === '/' ||
      currentPath === '' ||
      currentPath === '/thirdapp/ecocase/'
    ) {
      sessionStorage.removeItem('currentProjectId'); // 清除保存的projectId
      sessionStorage.removeItem('selectedTreeNode');
      return null;
    }
    let matchCases;
    let matchReview;
    if (currentPath.includes('/thirdapp/ecocase/')) {
      // 修改正则表达式以正确匹配完整路径
      matchCases = currentPath.match(
        /\/thirdapp\/ecocase\/case\/caseList\/(\d+)/,
      );
      matchReview = currentPath.match(
        /\/thirdapp\/ecocase\/review\/reviewList\/(\d+)/,
      );
    } else {
      matchCases = currentPath.match(/\/case\/caseList\/(\d+)/);
      matchReview = currentPath.match(/\/review\/reviewList\/(\d+)/);
    }

    if (matchCases) {
      return matchCases[1];
    } else if (matchReview) {
      return matchReview[1];
    }

    return sessionStorage.getItem('currentProjectId');
  };
  // 获取用户角色信息
  getUserRole = () => {
    const userInfo = sessionStorage.getItem('userInfo');
    if (userInfo) {
      try {
        const parsedInfo = JSON.parse(userInfo);
        return parsedInfo.isAdmin;
      } catch (error) {
        return false;
      }
    }
    return false;
  };
  // 查询当前用户是否有review内容
  getReviewContent = () => {
    request('/review/isReview', {
      method: 'GET',
    })
    .then(res => {    
      if(res.code === 200){
         this.setState({
          isReview: res.data
         })
      }
    })
    .catch(err => {
      console.error('获取review内容失败:', err);
    });
  }
  // 渲染用户信息弹窗
  renderUserPopover = () => {
    const userInfo = sessionStorage.getItem('userInfo');
    const username = userInfo ? JSON.parse(userInfo).username : '未知用户';

    return (
      <div className={styles.userPopover}>
        <div className={styles.username}>{username}</div>
        <div
          className={styles.menuItem}
          onClick={() => this.setState({ passwordModalVisible: true })}
        >
          修改密码
        </div>
        <div
          className={styles.logoutBtn}
          onClick={() => {
            sessionStorage.clear();
            Cookies.remove('userName'); // 离线版本时，设置用户名兼容线上版本 以为线上版本取的是userName(有驼峰)
            Cookies.remove('username');
            // 获取当前hash路由路径
            const currentPath = window.location.hash.slice(1); // 去掉#号
            const redirectUrl = encodeURIComponent(currentPath);
            router.push(`/login?jumpto=${redirectUrl}`);
          }}
        >
          退出登录
        </div>
      </div>
    );
  };
  render() {
    const { passwordModalVisible, isReview } = this.state;
    const isAdmin = this.getUserRole();
    const { children } = this.props;
    const isOffline = process.env.APP_VERSION === 'offline'; // 判断是否是
    const projectId = this.getCurrentProjectId();
    
    return (
      <Layout className={styles.layout}>
        <Sider collapsed width={200} theme="dark" className={styles.sider}>
          {/* <div className={styles.logo}>EasyCase</div> */}
          <Menu
            mode="inline"
            theme="dark"
            defaultSelectedKeys={[window.location.hash.slice(1) || '/']}
          >
            <Menu.Item key="/" onClick={() => router.push('/')}>
              <Icon type="home" />
              <span>首页</span>
            </Menu.Item>
            {projectId && (
              <Menu.Item
                key={`/case/caseList/${projectId}`}
                onClick={() => router.push(`/case/caseList/${projectId}`)}
              >
                <Icon type="profile" />
                <span>用例管理</span>
              </Menu.Item>
            )}
            {projectId && (
              
              <Menu.Item
                key={`/review/reviewList/${projectId}`}
                onClick={() => router.push(`/review/reviewList/${projectId}`)}
              >
               
                  <Icon type="audit" className={`menuIcon ${isReview ? 'hasReview' : ''}`} />
                  <span>review管理</span>
              </Menu.Item>
            )}
            {projectId && (
              <Menu.Item
                key={`/convert/${projectId}`}
                onClick={() => router.push(`/convert/${projectId}`)}
              >
                <Icon type="swap" />
                  <span>用例转换</span>
              </Menu.Item>
            )}
            {isAdmin && [
              <Menu.Item
                key="/admin/users"
                onClick={() => router.push('/admin/users')}
              >
                <Icon type="team" />
                <span>用户管理</span>
              </Menu.Item>,
              <Menu.Item
                key="/admin/project"
                onClick={() => router.push('/admin/project')}
              >
                <Icon type="project" />
                <span>项目管理</span>
              </Menu.Item>,
            ]}
          </Menu>
          {isOffline && (
            <div className={styles.userInfoWrapper}>
              <Popover
                placement="right"
                content={this.renderUserPopover()}
                trigger="click"
              >
                <img
                  src={userInfoIcon}
                  className={styles.userInfoIcon}
                  alt="用户信息"
                  title="用户信息"
                />
              </Popover>
            </div>
          )}
        </Sider>
        <Layout>
          <Content className={styles.content}>{children}</Content>
        </Layout>
        <PasswordModal
          visible={passwordModalVisible}
          onCancel={() => this.setState({ passwordModalVisible: false })}
        />
      </Layout>
    );
  }
}

export default BasicLayout;
