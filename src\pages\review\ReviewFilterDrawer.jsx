import React from 'react';
import { But<PERSON>, <PERSON>, Col, Select, Input, Icon } from 'antd';
import request from '@/utils/axios';
import './ReviewList.scss';

const { Option } = Select;

class ReviewFilterDrawer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      title: '',
      review: undefined,
      revise: undefined,
      status: undefined,
      userList: []
    };
  }
  componentDidMount() {
    this.getUserList();
  }
  // 获取用户列表
  getUserList = () => {   
    request(`/user/companyUserList`, {
      method: 'GET',
      params: {
        id: this.props.productLineId
      }
    })
    .then(res => {
      if (res.code === 200) {
        const userList = (res.data || []).map(user => ({
          label: user.username,
          value: user.id,
          key: user.id
        }));
        
        this.setState({ 
          userList,
        });
      } else {
        console.error('获取用户列表失败:', res);
        this.setState({ loading: false });
      }
    })
    .catch(err => {
      console.error('获取用户列表出错:', err);
      this.setState({ loading: false });
    });
  };
  handletitleChange = (e) => {
    this.setState({ title: e.target.value });
  };

  handleReviewChange = (value) => {
    this.setState({ review: value });
  };

  handleReviseChange = (value) => {
    this.setState({ revise: value });
  };

  handleStatusChange = (value) => {
    this.setState({ status: value });
  };

  handleReset = () => {
    this.setState({
      title: '',
      review: undefined,
      revise: undefined,
      status: undefined
    });
    this.props.onSearch({
      title: '',
      review: undefined,
      revise: undefined,
      status: undefined
    });
  };

  handleSearch = () => {
    const { title, review, revise, status } = this.state;
    this.props.onSearch({
      title,
      review,
      revise,
      status
    });
  };

  render() {
    const { visible, onClose, filterStatus } = this.props;
    const { title, review, revise, status, userList } = this.state;

    const statusOptions = [
      { value: 1, label: 'Review中' },
      { value: 2, label: '修订中' },
      { value: 3, label: '完成' },
      { value: 4, label: '过期' }
    ];

    return (
      <div className={`filter-case-modal-wrapper ${filterStatus}`}>
        <div className="filter-case-header">
          <span>快速筛选</span>
          <Icon onClick={onClose} type="close" />
        </div>
        <Row>
          <Col span={24} className="m-b-24">
            <div className="filter-item">
              <Input
                placeholder="用例名称"
                style={{ width: '100%' }}
                value={title}
                onChange={this.handletitleChange}
                allowClear
              />
            </div>
          </Col>
          <Col span={24} className="m-b-24">
            <div className="filter-item">
              <Select
                style={{ width: '100%' }}
                placeholder="请选择Review人"
                value={review}
                onChange={this.handleReviewChange}
                optionFilterProp="children"
                allowClear
              >
                {userList.map(item => (
                  <Option key={item.value} value={item.label}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col span={24} className="m-b-24">
            <div className="filter-item">
              <Select
                style={{ width: '100%' }}
                placeholder="请选择修订人"
                value={revise}
                onChange={this.handleReviseChange}
                optionFilterProp="children"
                allowClear
              >
                {userList.map(item => (
                  <Option key={item.value} value={item.label}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col span={24} className="m-b-24">
            <div className="filter-item">
              <Select
                style={{ width: '100%' }}
                placeholder="请选择状态"
                value={status}
                onChange={this.handleStatusChange}
                allowClear
              >
                {statusOptions.map(item => (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
        </Row>
        <div className="button-bottom">
          <Button onClick={this.handleReset} style={{ marginRight: 8 }}>
            重置
          </Button>
          <Button onClick={this.handleSearch} type="primary">
            搜索
          </Button>
        </div>
      </div>
    );
  }
}

export default ReviewFilterDrawer;