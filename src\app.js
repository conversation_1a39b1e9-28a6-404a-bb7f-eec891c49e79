// import utils from './utils';
// utils.setcookie('username', 'user');
import request from '@/utils/axios'; // 导入封装的request
import Cookies from 'js-cookie';
import { router } from 'umi';
// 脑图图片预览样式文件
import './components/react-mindmap-editor/assets/kityminder-core/kityminder.core.css';
import { NextLoading } from '../src/utils/loading';

export const dva = {
  config: {
    onError(err) {
      err.preventDefault()
      // eslint-disable-next-line
      console.error(err.message);
    },
     
  },
}
// 每次刷新调用登录人用户信息接口
export async function render(oldRender) {
  // Cookies.set('jwt', 'asdagdadgajhgjhga');
  // Cookies.set('userName', 'siliangjiang002');
  const isOfflineVersion = process.env.APP_VERSION === 'offline';
  const isLoggedIn = Cookies.get('username');
  const isLoginPage = window.location.pathname === '/login' || window.location.pathname === '/thirdapp/ecocase/login';
  
  // 如果是登录页面，直接渲染，不调用用户信息接口
  if (isLoginPage) {
    oldRender();
    return;
  }
  
  // 离线版本且未登录时，直接渲染登录页面
  if (isOfflineVersion && !isLoggedIn) {
    oldRender();
    return;
  }
  try {
    NextLoading.start()  //全局loading
    const res= await request('/user/getCurrentUser', {
      method: 'GET',
    });
    if(res.code===200){
      const userInfo = {
        username: res.data.username,
        authorityName: res.data.authorityName,
        userId: res.data.id,
        isAdmin: res.data.authorityName==="ROLE_ADMIN"?true:false,
      }
      sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
      // 离线版本时，设置用户名兼容线上版本 以为线上版本取的是userName(有驼峰)
      if(isOfflineVersion){
       Cookies.set('userName', res.data.username);
      }

    }   

  } catch (error) {
    console.error('获取用户信息失败:', error);
    // NextLoading.done(600)
  }finally {
    // 根据请求时间动态设置延迟
    NextLoading.done(600); // 300ms 后移除 loading
  }
  oldRender();
}
