.revise_container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    border-bottom: 1px solid #e8e8e8;

    .left {
      display: flex;
      align-items: center;
      gap: 12px;
  
      .backIcon {
        font-size: 16px;
        cursor: pointer;
        color: rgba(0, 0, 0, 0.45);
        
        &:hover {
          color: #1890ff;
        }
      }
  
      .titleWrapper {
        display: flex;
        flex-direction: column;
        
        .title {
          font-size: 14px;
          color: #000000d9;
          line-height: 1.5;
        }
  
        .updateTime {
          color: rgba(0, 0, 0, 0.45);
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
      gap: 24px;
      color: #00000073;
      font-size: 14px;
    }
  }

  .revise_content {
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;

    .mindmap {
      flex: 1;
      height: 100%;
    }

    .annotationContainer {
      position: relative;
      
      .collapseBtn {
        position: absolute;
        left: -16px;
        top: 50%;
        transform: translateY(-50%);
        width: 16px;
        height: 48px;
        background: #fff;
        border: 1px solid #e8e8e8;
        border-right: none;
        border-radius: 4px 0 0 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 1;

        &:hover {
          background: #f5f5f5;
        }
      }

      .annotation {
        width: 415px;
        height: 100%;
        background: #fff;
        border-left: 1px solid #e8e8e8;
        transition: all 0.3s;
        display: flex;
        flex-direction: column;

        &.collapsed {
          width: 0;
          overflow: hidden;
        }

        .annotationHeader {
          padding: 16px;
          border-bottom: 1px solid #e8e8e8;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .title {
            font-size: 16px;
            font-weight: 500;
            color: #000000d9;
          }

          .filter {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #00000073;

            .filterDropdown {
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 4px;
              color: #00000073;

              &:hover {
                color: #1890ff;
              }

              .anticon {
                font-size: 12px;
              }
            }
          }
        }

        .annotationList {
          flex: 1;
          overflow-y: auto;
          padding: 8px;
          
          .emptyState {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 32px 0;
          }

          /* 自定义滚动条样式 */
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
              background: #a8a8a8;
            }
          }
        }
      }
    }
  }
}

// 下拉菜单样式覆盖
:global {
  .ant-dropdown-menu {
    padding: 4px 0;
    min-width: 80px;

    .ant-dropdown-menu-item {
      padding: 8px 16px;
      color: #00000073;

      &:hover {
        background-color: #f5f5f5;
        color: #1890ff;
      }

      &-selected {
        color: #1890ff;
        font-weight: 600;
        background-color: #e6f7ff;
      }
    }
  }
} 