import React from 'react';
import './less/login.less';
import { Form, Input, Button, Icon, message } from 'antd';
import request from '@/utils/axios';
import utils from '@/utils';
import { router } from 'umi';
import Cookies from 'js-cookie';
class LogIn extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      type: '1', // 当前为什么类型 1：登录 2： 注册
      loading: false, // 点击注册登录
    };
  }

  typeChange = type => {
    this.setState({ type }, () => {
      this.props.form.resetFields();
    });
  };
  // 获取用户信息
  getUserInfo = async () => {
    try {
      const res = await request('/user/getCurrentUser', {
        method: 'GET',
      });
      if (res && res.code === 200) {
        const userInfo = {
          username: res.data.username,
          authorityName: res.data.authorityName,
          userId: res.data.id,
          isAdmin: res.data.authorityName === 'ROLE_ADMIN' ? true : false,
        };
        Cookies.set('userName', userInfo.username);
        return userInfo;
      } else {
        message.error('获取用户信息失败');
        return null;
      }
    } catch (error) {
      message.error('获取用户信息失败');
      return null;
    }
  };

  // 处理页面跳转
  handleRedirect = () => {
    const jumpto = utils.getHashQueryString('jumpto');
    if (jumpto) {
      router.push(decodeURIComponent(jumpto));
    } else {
      router.push('/');
    }
  };
  onOk = () => {
    this.props.form.validateFields(async (error, value) => {
      if (error) return;
      this.setState({ loading: true });
      if (this.state.type === '1') {
        // 登录
        // request(`/user/login`, {
        //   method: 'POST',
        //   body: { ...value },
        // }).then(res => {
        //   if (res && res.code === 200) {
        //     message.success('登陆成功')
        //     // window.location.href = utils.getQueryString('jumpto')
        //     const jumpto = utils.getHashQueryString('jumpto');
        //     // 如果有跳转地址，使用 router 进行跳转
        //     if (jumpto) {
        //       router.push(decodeURIComponent(jumpto));
        //     } else {
        //       router.push('/');
        //     }
        //   } else {
        //     message.error(res.msg)
        //   }
        //   this.setState({ loading: false })
        // })
        try {
          const loginRes = await request(`/user/login`, {
            method: 'POST',
            body: { ...value },
          });

          if (loginRes.code === 200) {
            const userInfo = await this.getUserInfo();
            if (userInfo) {
              sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
              message.success('登录成功');
              this.handleRedirect();
            }
          } else {
            message.error(loginRes.msg);
          }
        } catch (error) {
          message.error('登录失败');
        } finally {
          this.setState({ loading: false });
        }
      } else {
        // 注册
        // request(`/user/register`, {
        //   method: 'POST',
        //   body: { ...value },
        // }).then(res => {
        //   if (res && res.code === 200) {
        //     message.success('注册成功')
        //     // window.location.href = utils.getQueryString('jumpto')
        //     const jumpto = utils.getHashQueryString('jumpto');
        //     // 如果有跳转地址，使用 router 进行跳转
        //     if (jumpto) {
        //       router.push(decodeURIComponent(jumpto));
        //     } else {
        //       router.push('/');
        //     }
        //   } else {
        //     message.error(res.msg)
        //   }
        //   this.setState({ loading: false })
        // })
        try {
          const registerRes = await request(`/user/register`, {
            method: 'POST',
            body: { ...value },
          });

          if (registerRes.code === 200) {
            message.success('注册成功');
            // 注册成功后调用登录接口
            const loginRes = await request(`/user/login`, {
              method: 'POST',
              body: { username: value.username, password: value.password },
            });
            
            if (loginRes.code === 200) {
              const userInfo = await this.getUserInfo();
              if (userInfo) {
                sessionStorage.setItem('userInfo', JSON.stringify(userInfo));
                message.success('登录成功');
                this.handleRedirect();
              }
            } else {
              message.error(loginRes.msg || '登录失败');
            }
          } else {
            message.error(registerRes.msg || '注册失败');
          }
        } catch (error) {
          message.error('注册失败');
        } finally {
          this.setState({ loading: false });
        }
      }
    });
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { type, loading } = this.state;
    return (
      <div className="login">
        <div className="card">
          <div className="title">
            EcoCase<span>测试用例管理平台</span>
          </div>
          <span
            className={type === '1' ? 'btn btn_active' : 'btn'}
            onClick={() => this.typeChange('1')}
          >
            登录
          </span>
          <span
            className={type === '2' ? 'btn btn_active' : 'btn'}
            onClick={() => this.typeChange('2')}
          >
            注册
          </span>
          <div className="input">
            <Form.Item label="">
              {getFieldDecorator('username', {
                rules: [{ required: true, message: '请填写账号' }],
                initialValue: undefined,
              })(<Input placeholder="账号" prefix={<Icon type="user" />} />)}
            </Form.Item>
            {type === '1' && (
              <Form.Item label="">
                {getFieldDecorator('password', {
                  rules: [{ required: true, message: '请填写密码' }],
                  initialValue: undefined,
                })(
                  <Input.Password
                    placeholder="密码"
                    prefix={<Icon type="lock" />}
                  />,
                )}
              </Form.Item>
            )}
            {type === '2' && (
              <Form.Item label="">
                {getFieldDecorator('password', {
                  rules: [{ required: true, message: '请填写密码' }],
                  initialValue: undefined,
                })(
                  <Input.Password
                    placeholder="密码"
                    prefix={<Icon type="lock" />}
                  />,
                )}
              </Form.Item>
            )}
          </div>
          <Button
            type="primary"
            className="onBtn"
            loading={loading}
            onClick={() => this.onOk()}
          >
            {type === '1' ? '登录' : '注册并登录'}
          </Button>
        </div>
      </div>
    );
  }
}

export default Form.create()(LogIn);
