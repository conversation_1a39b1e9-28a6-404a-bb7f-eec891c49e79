export const removeCommentCount = (data) => {
    if (!data || typeof data !== 'object') return data;
    
    if (data.root) {
      // 处理根节点
      const processNode = (node) => {
        if (node.data) {
          const { commentCount, ...rest } = node.data;
          node.data = rest;
        }
        if (node.children && Array.isArray(node.children)) {
          node.children.forEach(processNode);
        }
    };
      
      processNode(data.root);
    }
    return data;
  };