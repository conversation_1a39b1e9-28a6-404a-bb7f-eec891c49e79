{
  "env": {
    "browser": true,
    "commonjs": true,
    "es6": true
  },
  "parser": "babel-eslint",
  "extends": ["prettier", "eslint:recommended", "plugin:react/recommended"],
  "parserOptions": {
    "ecmaFeatures": {
      // 添加ES特性支持，使之能够识别ES6语法
      "jsx": true
    },
    "sourceType": "module",
    "ecmaVersion": 2017
  },
  "plugins": ["prettier", "react"],
  "rules": {
    "prettier/prettier": [
      "error",
      {
        "printWidth": 100,
        "semi": false,
        "singleQuote": true
      }
    ],
    "react/jsx-uses-react": "error",
    "react/jsx-uses-vars": "error",
    "react/react-in-jsx-scope": 0,
    "react/no-deprecated": 0,
    "react/no-string-refs": 1,
    "no-console": "error",
    // no-var
    "no-var": "error",
    // 要求或禁止 var 声明中的初始化
    "init-declarations": 2,
    // 禁止不必要的分号
    "no-extra-semi": "error",
    // 强制使用一致的换行风格
    "linebreak-style": ["error", "unix"],
    // 空格2个
    "indent": ["error", 2, { "SwitchCase": 1 }],
    // 指定数组的元素之间要以空格隔开(,后面)， never参数：[ 之前和 ] 之后不能带空格，always参数：[ 之前和 ] 之后必须带空格
    "array-bracket-spacing": [2, "never"],
    // 在块级作用域外访问块内定义的变量是否报错提示
    "block-scoped-var": 0,
    // if while function 后面的{必须与if在同一行，java风格。
    "brace-style": [2, "1tbs", { "allowSingleLine": true }],
    // 双峰驼命名格式
    "camelcase": 2,
    // 数组和对象键值对最后一个逗号， never参数：不能带末尾的逗号, always参数：必须带末尾的逗号，
    "comma-dangle": [2, "always-multiline"],
    // 控制逗号前后的空格
    "comma-spacing": [2, { "before": false, "after": true }],
    // 控制逗号在行尾出现还是在行首出现
    "comma-style": [2, "last"],
    // 圈复杂度
    // "complexity": [2, 9],
    // 以方括号取对象属性时，[ 后面和 ] 前面是否需要空格, 可选参数 never, always
    "computed-property-spacing": [2, "never"],
    // TODO 关闭 强制方法必须返回值，TypeScript强类型，不配置
    // 'consistent-return': 0
    "react/prop-types": [1]
  }
}
