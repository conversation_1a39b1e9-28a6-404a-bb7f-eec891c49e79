export const mockData = [
    {
        "id": "file_1751594437126_x05rwifu6",
        "name": "template_转换结果.xmind",
        "size": 3916,
        "type": "xmind",
        "uploadTime": "2025-07-04 10:00",
        "expiryTime": "2025-07-11T02:00:37.126Z",
        "status": "success",
        "conversion": "XMind → Excel",
        "data": {
            "mindmapData": {
                "root": {
                    "data": {
                        "text": "系统模块",
                        "imageSize": {}
                    },
                    "children": [
                        {
                            "data": {
                                "text": "功能模块1",
                                "imageSize": {}
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\r\n1xxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\r\n2xxxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\r\n3xxxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "data": {
                                "text": "功能模块2",
                                "imageSize": {}
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\r\n4xxxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\r\n5xxxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\r\n6xxxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                "template": "default",
                "theme": "fresh-blue",
                "version": "1.4.43"
            },
            "tableData": [
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块1",
                    "testPoint": "1xxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\r\n2、xxx\r\n3、xxx",
                    "expectedResult": "1、xxx\r\n2、xxx",
                    "actualResult": ""
                },
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块1",
                    "testPoint": "2xxxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\r\n2、xxx\r\n3、xxx",
                    "expectedResult": "1、xxx\r\n2、xxx",
                    "actualResult": ""
                },
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块1",
                    "testPoint": "3xxxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\r\n2、xxx\r\n3、xxx",
                    "expectedResult": "1、xxx\r\n2、xxx",
                    "actualResult": ""
                },
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块2",
                    "testPoint": "4xxxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\r\n2、xxx\r\n3、xxx",
                    "expectedResult": "1、xxx\r\n2、xxx",
                    "actualResult": ""
                },
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块2",
                    "testPoint": "5xxxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\r\n2、xxx\r\n3、xxx",
                    "expectedResult": "1、xxx\r\n2、xxx",
                    "actualResult": ""
                },
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块2",
                    "testPoint": "6xxxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\r\n2、xxx\r\n3、xxx",
                    "expectedResult": "1、xxx\r\n2、xxx",
                    "actualResult": ""
                }
            ],
            "original": {
                "root": {
                    "data": {
                        "text": "系统模块",
                        "imageSize": {}
                    },
                    "children": [
                        {
                            "data": {
                                "text": "功能模块1",
                                "imageSize": {}
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\r\n1xxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\r\n2xxxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\r\n3xxxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "data": {
                                "text": "功能模块2",
                                "imageSize": {}
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\r\n4xxxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\r\n5xxxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\r\n6xxxxx",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\r\nxxxxxxxxxx",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\r\n1、xxx\r\n2、xxx\r\n3、xxx",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\r\n1、xxx\r\n2、xxx",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                "template": "default",
                "theme": "fresh-blue",
                "version": "1.4.43"
            }
        }
    },
    {
        "id": "file_1751593790622_si8ph0sgl",
        "name": "转换测试1.xmind",
        "size": 15532,
        "type": "xmind",
        "uploadTime": "2025-07-04 09:49",
        "expiryTime": "2025-07-11T01:49:50.622Z",
        "status": "success",
        "conversion": "XMind → Excel",
        "data": {
            "mindmapData": {
                "root": {
                    "data": {
                        "text": "转换测试1",
                        "imageSize": {}
                    },
                    "children": [
                        {
                            "data": {
                                "text": "订单模块",
                                "imageSize": {}
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\n订单创建",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\n购物车有商品且用户已登录",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1. 进入购物车页面\n2. 点击\"去结算\"按钮\n3. 填写收货信息\n4. 选择支付方式\n5. 提交订单",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n订单创建成功，显示订单详情页面",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "data": {
                                "text": "支付模块",
                                "imageSize": {}
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\n在线支付",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\n订单已创建且选择在线支付",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1. 在订单支付页面\n2. 选择支付方式\n3. 输入支付密码或扫码支付\n4. 确认支付",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n支付成功，订单状态更新为已支付",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "data": {
                                "text": "评价模块",
                                "imageSize": {}
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\n评价提交",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\n用户已购买商品且订单已完成",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1. 进入已购买商品页面\n2. 点击\"评价\"按钮\n3. 选择评分星级\n4. 输入评价内容\n5. 提交评价",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n评价提交成功，商品页面显示用户评价",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                "template": "default",
                "theme": "fresh-blue",
                "version": "1.4.43"
            },
            "tableData": [
                {
                    "caseName": "转换测试1",
                    "functionModule": "订单模块",
                    "testPoint": "订单创建",
                    "precondition": "购物车有商品且用户已登录",
                    "testSteps": "1. 进入购物车页面\n2. 点击\"去结算\"按钮\n3. 填写收货信息\n4. 选择支付方式\n5. 提交订单",
                    "expectedResult": "订单创建成功，显示订单详情页面",
                    "actualResult": ""
                },
                {
                    "caseName": "转换测试1",
                    "functionModule": "支付模块",
                    "testPoint": "在线支付",
                    "precondition": "订单已创建且选择在线支付",
                    "testSteps": "1. 在订单支付页面\n2. 选择支付方式\n3. 输入支付密码或扫码支付\n4. 确认支付",
                    "expectedResult": "支付成功，订单状态更新为已支付",
                    "actualResult": ""
                },
                {
                    "caseName": "转换测试1",
                    "functionModule": "评价模块",
                    "testPoint": "评价提交",
                    "precondition": "用户已购买商品且订单已完成",
                    "testSteps": "1. 进入已购买商品页面\n2. 点击\"评价\"按钮\n3. 选择评分星级\n4. 输入评价内容\n5. 提交评价",
                    "expectedResult": "评价提交成功，商品页面显示用户评价",
                    "actualResult": ""
                }
            ],
            "original": {
                "root": {
                    "data": {
                        "text": "转换测试1",
                        "imageSize": {}
                    },
                    "children": [
                        {
                            "data": {
                                "text": "订单模块",
                                "imageSize": {}
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\n订单创建",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\n购物车有商品且用户已登录",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1. 进入购物车页面\n2. 点击\"去结算\"按钮\n3. 填写收货信息\n4. 选择支付方式\n5. 提交订单",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n订单创建成功，显示订单详情页面",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "data": {
                                "text": "支付模块",
                                "imageSize": {}
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\n在线支付",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\n订单已创建且选择在线支付",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1. 在订单支付页面\n2. 选择支付方式\n3. 输入支付密码或扫码支付\n4. 确认支付",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n支付成功，订单状态更新为已支付",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "data": {
                                "text": "评价模块",
                                "imageSize": {}
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\n评价提交",
                                        "imageSize": {}
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\n用户已购买商品且订单已完成",
                                                "imageSize": {}
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1. 进入已购买商品页面\n2. 点击\"评价\"按钮\n3. 选择评分星级\n4. 输入评价内容\n5. 提交评价",
                                                        "imageSize": {}
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n评价提交成功，商品页面显示用户评价",
                                                                "imageSize": {}
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                "template": "default",
                "theme": "fresh-blue",
                "version": "1.4.43"
            }
        }
    },
    {
        "id": "file_1751592897462_z8eetwwmt",
        "name": "template.xls",
        "size": 7151,
        "type": "excel",
        "uploadTime": "2025-07-04 09:34",
        "expiryTime": "2025-07-11T01:34:57.462Z",
        "status": "success",
        "conversion": "Excel → XMind",
        "data": {
            "mindmapData": {
                "root": {
                    "data": {
                        "text": "系统模块"
                    },
                    "children": [
                        {
                            "data": {
                                "text": "功能模块1"
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\n1xxxx"
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\nxxxxxxxxxx"
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1、xxx\n2、xxx\n3、xxx"
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n1、xxx\n2、xxx"
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\n2xxxxx"
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\nxxxxxxxxxx"
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1、xxx\n2、xxx\n3、xxx"
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n1、xxx\n2、xxx"
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\n3xxxxx"
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\nxxxxxxxxxx"
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1、xxx\n2、xxx\n3、xxx"
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n1、xxx\n2、xxx"
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "data": {
                                "text": "功能模块2"
                            },
                            "children": [
                                {
                                    "data": {
                                        "text": "测试点:\n4xxxxx"
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\nxxxxxxxxxx"
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1、xxx\n2、xxx\n3、xxx"
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n1、xxx\n2、xxx"
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\n5xxxxx"
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\nxxxxxxxxxx"
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1、xxx\n2、xxx\n3、xxx"
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n1、xxx\n2、xxx"
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "data": {
                                        "text": "测试点:\n6xxxxx"
                                    },
                                    "children": [
                                        {
                                            "data": {
                                                "text": "前置条件:\nxxxxxxxxxx"
                                            },
                                            "children": [
                                                {
                                                    "data": {
                                                        "text": "测试步骤:\n1、xxx\n2、xxx\n3、xxx"
                                                    },
                                                    "children": [
                                                        {
                                                            "data": {
                                                                "text": "预期结果:\n1、xxx\n2、xxx"
                                                            }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            },
            "tableData": [
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块1",
                    "testPoint": "1xxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\n2、xxx\n3、xxx",
                    "expectedResult": "1、xxx\n2、xxx",
                    "actualResult": ""
                },
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块1",
                    "testPoint": "2xxxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\n2、xxx\n3、xxx",
                    "expectedResult": "1、xxx\n2、xxx",
                    "actualResult": ""
                },
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块1",
                    "testPoint": "3xxxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\n2、xxx\n3、xxx",
                    "expectedResult": "1、xxx\n2、xxx",
                    "actualResult": ""
                },
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块2",
                    "testPoint": "4xxxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\n2、xxx\n3、xxx",
                    "expectedResult": "1、xxx\n2、xxx",
                    "actualResult": ""
                },
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块2",
                    "testPoint": "5xxxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\n2、xxx\n3、xxx",
                    "expectedResult": "1、xxx\n2、xxx",
                    "actualResult": ""
                },
                {
                    "caseName": "系统模块",
                    "functionModule": "功能模块2",
                    "testPoint": "6xxxxx",
                    "precondition": "xxxxxxxxxx",
                    "testSteps": "1、xxx\n2、xxx\n3、xxx",
                    "expectedResult": "1、xxx\n2、xxx",
                    "actualResult": ""
                }
            ],
            "original": {
                "sheetName": "测试用例",
                "data": [
                    [
                        "用例名称",
                        "功能模块",
                        "测试点",
                        "前置条件",
                        "测试步骤",
                        "预期结果",
                        "实际结果"
                    ],
                    [
                        "系统模块",
                        "功能模块1",
                        "1xxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ],
                    [
                        "系统模块",
                        "功能模块1",
                        "2xxxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ],
                    [
                        "系统模块",
                        "功能模块1",
                        "3xxxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ],
                    [
                        "系统模块",
                        "功能模块2",
                        "4xxxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ],
                    [
                        "系统模块",
                        "功能模块2",
                        "5xxxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ],
                    [
                        "系统模块",
                        "功能模块2",
                        "6xxxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ]
                ],
                "headers": [
                    "用例名称",
                    "功能模块",
                    "测试点",
                    "前置条件",
                    "测试步骤",
                    "预期结果",
                    "实际结果"
                ],
                "rows": [
                    [
                        "系统模块",
                        "功能模块1",
                        "1xxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ],
                    [
                        "系统模块",
                        "功能模块1",
                        "2xxxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ],
                    [
                        "系统模块",
                        "功能模块1",
                        "3xxxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ],
                    [
                        "系统模块",
                        "功能模块2",
                        "4xxxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ],
                    [
                        "系统模块",
                        "功能模块2",
                        "5xxxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ],
                    [
                        "系统模块",
                        "功能模块2",
                        "6xxxxx",
                        "xxxxxxxxxx",
                        "1、xxx\n2、xxx\n3、xxx",
                        "1、xxx\n2、xxx",
                        ""
                    ]
                ]
            }
        }
    }
]