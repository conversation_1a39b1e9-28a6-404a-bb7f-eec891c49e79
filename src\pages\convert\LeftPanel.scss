.left-panel {
  width: 350px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: visible;
  position: relative;
  transition: all 0.3s ease;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      font-size: 16px;
      color: #303133;
    }

    .mode-toggle-btn {
      padding: 4px 8px;
      font-size: 12px;
      
      &:hover {
        background-color: #f5f5f5;
      }
    }
  }

  .case-tree {
    padding: 16px;
    flex: 1;
    overflow-y: auto;
    
    .ant-tree {
      background: transparent;
      
      .ant-tree-node-content-wrapper {
        padding: 4px 8px;
        border-radius: 4px;
        
        &:hover {
          background-color: #f5f5f5;
        }
        
        &.ant-tree-node-selected {
          background-color: #e6f7ff;
        }
      }
    }
  }

  .panel-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .file-list {
      flex: 1;
      overflow-y: auto;
      padding: 16px;

      .file-item {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 12px;
        border: 1px solid #f0f0f0;
        background: #fafafa;

        &:hover {
          background: #f5f7fa;
          border-color: #d3d3d3;
          
          .file-actions {
            opacity: 1;
          }
        }

        &.active {
          background: #e6f7ff;
          border-color: #409eff;
        }

        .file-info {
          display: flex;
          align-items: center;
          flex: 1;
          min-width: 0;

          .file-icon {
            margin-right: 12px;
            flex-shrink: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            background: #f8f9fa;

            .anticon {
              font-size: 16px;
            }
          }

          .file-details {
            min-width: 0;
            flex: 1;

            .file-name {
              font-size: 14px;
              color: #303133;
              font-weight: 500;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-bottom: 4px;
            }

            .file-meta {
              font-size: 12px;
              color: #909399;
              margin-bottom: 2px;
            }

            .file-conversion {
              font-size: 11px;
              color: #67c23a;
              font-weight: 500;
            }

            .file-mode {
              font-size: 11px;
              color: #409eff;
              margin-top: 2px;
            }
          }
        }

        .file-actions {
          display: flex;
          align-items: center;
          gap: 8px;
          opacity: 0;
          transition: opacity 0.2s ease;

          .delete-icon {
            width: 20px;
            height: 20px;
            color: #f56c6c;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 4px;
            padding: 2px;

            &:hover {
              background: rgba(245, 108, 108, 0.1);
              color: #f56c6c;
              transform: scale(1.1);
            }
          }
        }
      }

      .empty-state {
        height: 80%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: 1;
        text-align: center;
        color: #909399;
        min-height: 0;

        .empty-icon {
          font-size: 48px;
          margin-bottom: 16px;
          color: #c0c4cc;
        }

        .empty-title {
          font-size: 16px;
          margin-bottom: 8px;
          color: #606266;
        }

        .empty-description {
          font-size: 14px;
          line-height: 1.5;
        }
      }

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }

  /* 切换按钮样式 */
  .panel-toggle {
    position: absolute;
    top: 50%;
    right: -15px;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;

    &:hover {
      background: #f5f7fa;
      border-color: #409eff;
      
      .toggle-icon {
        color: #409eff;
      }
    }

    .toggle-icon {
      transition: all 0.3s ease;
      color: #606266;
      font-size: 14px;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }

  /* 收缩状态样式 */
  &.collapsed {
    width: 0;
    min-width: 0;
    
    .panel-header,
    .panel-content {
      opacity: 0;
      visibility: hidden;
    }
  }
}





/* 响应式调整 */
@media (max-width: 1200px) {
  .left-panel {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .left-panel {
    width: 100%;
    max-width: 320px;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  
  .ant-spin {
    margin-bottom: 12px;
  }
  
  div {
    font-size: 14px;
  }
} 
