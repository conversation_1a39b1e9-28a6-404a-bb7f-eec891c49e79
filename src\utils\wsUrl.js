/**
 * 获取WebSocket URL
 * @returns {string} WebSocket URL
 */
export const getWsUrl = () => {
  const hostname = window.location.hostname;
  const isDev = process.env.NODE_ENV === 'development';
  const isOffline = process.env.APP_VERSION === 'offline'; // 离线版
  const isOnline = process.env.APP_VERSION === 'online'; // 在线版

  if (isDev) {
    return 'http://**************:8095';
  } else if (isOffline) {
    // 生产环境离线版
    return `http://${hostname}:8095`;
  } else if (isOnline) {
    // 生产环境
    if (hostname == 'service.eco.tencent.com') {
      return `https://${hostname}`; // 在线版
    } else {
      return `http://${hostname}:8095`; // 离线版
    }
  } else {
    return `https://${hostname}`;
  }
};

export default getWsUrl;
