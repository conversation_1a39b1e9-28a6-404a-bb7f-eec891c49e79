.review-list-container {
//   padding: 16px;
margin: 15px;
  background: #fff;
  
  .m-b-10 {
    margin-bottom: 10px;
  }

  .text-right {
    text-align: right;
  }

  .table-ellipsis {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .icon-bg {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background: #f5f5f5;
    border-radius: 2px;
    margin-right: 8px;

    &:hover {
      color: #1890ff;
      background: #e6f7ff;
    }
  }

  .border-a-redius-left {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .border-a-redius-right {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .ant-tabs-bar {
    margin: 0;
  }

  .ant-table-thead > tr > th {
    background: #fafafa;
  }

  .site-drawer-render-in-current-wrapper {
    position: relative;
    height: calc(100vh - 35px);
    padding: 16px;
    overflow: hidden;
    background: #fff;
  }

  .filter-case-modal-wrapper {
    position: fixed;
    top: 120px;
    right: -320px;
    width: 320px;
    height: calc(100vh - 250px);
    background-color: #fff;
    z-index: 1000;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
    padding: 0 16px;
    transition: right 0.3s ease-in-out;
    
    &.filter-show {
      right: 0;
    }
    
    &.filter-hide {
      right: -320px;
    }
    
    .filter-case-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #e8e8e8;
      margin-bottom: 16px;
      
      span {
        font-size: 16px;
        font-weight: 500;
      }
      
      .anticon {
        cursor: pointer;
        &:hover {
          color: #1890ff;
        }
      }
    }
    
    .m-b-24 {
      margin-bottom: 24px;
    }
    
    .filter-item {
      width: 100%;
    }
    
    .button-bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      padding: 10px 16px;
      text-align: right;
      background: #fff;
      border-top: 1px solid #e8e8e8;
    }
  }
} 