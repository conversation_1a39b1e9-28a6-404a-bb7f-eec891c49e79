import React from 'react';
import { Table, Button, Divider, message } from 'antd';
import Headers from '../../../layouts/headers';

const RoleManagement = () => {
  const columns = [
    {
      title: '角色名称',
      dataIndex: 'roleName',
      key: 'roleName',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <span>
          <Button type="link">编辑</Button>
          <Divider type="vertical" />
          <Button type="link">权限设置</Button>
          <Divider type="vertical" />
          <Button type="link" style={{ color: '#f5222d' }}>
            删除
          </Button>
        </span>
      ),
    },
  ];

  return (
    <section style={{ marginBottom: 30 }}>
      {/* <Headers /> */}
      <div className="role-management" style={{ padding: '24px' }}>
        <div style={{ marginBottom: 16 }}>
          <Button type="primary">新增角色</Button>
        </div>
        <Table columns={columns} dataSource={[]} rowKey="id" />
      </div>
    </section>
  );
};

export default RoleManagement;
