import React from 'react';
import { Button, Icon, message, Dropdown, Menu, Empty, Spin, Modal } from 'antd';
import styles from './index.less';
import AgileTCEditor from '../../components/react-mindmap-editor';
import request from '@/utils/axios';
import AnnotationItem from '../../components/AnnotationItem';
import Cookie from 'js-cookie';
import { removeCommentCount } from '@/utils/common';
import getWsUrl from '@/utils/wsUrl';
import router from 'umi/router';
class ReviewPage extends React.Component {
  constructor(props) {
    super(props);
    this.statusMap = {
      '全部': '全部',
      '1': '未处理',
      '2': '同意',
      '3': '忽略'
    };
    this.state = {
      isAnnotationVisible: true,
      loading: false,
      info: null,
      annotations: [],
      selectedAnnotationId: null,
      filterStatus: '全部',
      filterStatusName: '全部', // 添加用于显示的状态名称
      filterUser: '所有人',
      filterUserName: '所有人',
      userList: [],
      caseId: '',
      reviewId: '',
      annotationsLoading: false,
      reviewInfo: null,
      productLineId: null,
    };
    this.editorRef = React.createRef();
  }

  componentDidMount() {
    const { caseId, reviewId } = this.props.location.query;
    this.setState({ caseId, reviewId });   
    this.getReviewInfo();
    this.fetchAnnotations();
    // this.fetchUserList();
  }
    // 获取用户列表
    fetchUserList = async () => {
      request(`/user/companyUserList`, {
        method: 'GET',
        params: {
          id: this.state.productLineId,
        },
      })
        .then(res => {
          if (res.code === 200) {
            const userList = (res.data || []).map(user => ({
              name: user.username,
              id: user.id,
            }));
  
            this.setState({
              userList,
            });
          } else {
            console.error('获取用户列表失败:', res);
            message.error('获取用户列表失败: ' + (res.msg || '未知错误'));
          }
        })
        .catch(err => {
          console.error('获取用户列表出错:', err);
          message.error('获取用户列表失败，请检查网络连接');
          this.setState({ loading: false });
        });
    };
  handleNodeSelect = nodeId => {
    // 当节点被选中时,更新 selectedAnnotationId
    this.setState({ selectedAnnotationId: nodeId });

    // 找到对应的批注
    const annotation = this.state.annotations.find(
      item => item.nodeId === nodeId,
    );
    if (annotation) {
      // 如果找到对应的批注,滚动到视图中
      const element = document.getElementById(`annotation-${nodeId}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }
  };
  fetchAnnotations = async () => {
    this.setState({ annotationsLoading: true }); // 开始加载时设置状态
    const { caseId, reviewId } = this.props.location.query;  
    const { filterStatus, filterUser } = this.state;
    
    const params = {
      pageNum: 1,
      pageSize: 99999,
      reviewId: reviewId,
    };

    // 只有当不是默认值时才添加筛选参数
    if (filterStatus !== '全部') {
      params.status = filterStatus;
    }
    if (filterUser !== '所有人') {
      params.creator = filterUser;
    }

    request(`/comment/queryByPage`, {
      method: 'GET',
      params: params,
    }).then(res => {
      if (res.code === 200) {
        console.log('批注res', res);
        const arr =res.data.dataSources.map(item => {
          return {
            id:item.nodeId,
            nodeId:item.nodeId,
            nodeTitle:item.nodeName,
            nodeStatus:item.nodeStatus,
            comments:item.comments?.map(comment => {
              return {
                id:comment.id,
                username:comment.creatorName,
                content:comment.comment,
                time:comment.gmtCreated,
                status:comment.status,              
                statusName:comment.statusName,
                images:comment.commentFile?.filter(file => {
                  // 判断文件是否为图片(通过扩展名判断)
                  const fileName = file.fileName.toLowerCase();
                  return fileName.endsWith('.png') || 
                         fileName.endsWith('.jpg') || 
                         fileName.endsWith('.jpeg') || 
                         fileName.endsWith('.gif') || 
                         fileName.endsWith('.bmp') || 
                         fileName.endsWith('.webp');
                }).map(file => {
                  return {
                    id:file.id,
                    url:file.filePath,
                    name:file.fileName,
                  }
                }) || [],
                files:comment.commentFile?.filter(file => {
                  // 判断文件是否不是图片
                  const fileName = file.fileName.toLowerCase();
                  return !(fileName.endsWith('.png') || 
                         fileName.endsWith('.jpg') || 
                         fileName.endsWith('.jpeg') || 
                         fileName.endsWith('.gif') || 
                         fileName.endsWith('.bmp') || 
                         fileName.endsWith('.webp'));
                }).map(file => {
                  return {
                    id:file.id,
                    url:file.filePath,
                    name:file.fileName,
                  }
                }) || [],
                replies:comment.commentDiscuss?.map(reply => {
                  return {
                    id:reply.id,
                    username:reply.creatorName,
                    content:reply.comment,
                    time:reply.gmtCreated,
                    status:reply.status,
                  }
                }) || []
              }
            }) || []
          }
        }).sort((a, b) => {
          // 将nodeStatus=1的排到后面
          if (a.nodeStatus === 1 && b.nodeStatus !== 1) {
            return 1;
          }
          if (a.nodeStatus !== 1 && b.nodeStatus === 1) {
            return -1;
          }
          return 0;
        });
        this.setState({
          annotations: arr,
          annotationsLoading: false, // 加载完成后设置状态
        });
      }else{
        message.error('获取批注失败');
        this.setState({ annotationsLoading: false }); // 加载失败也要设置状态
      }
    }).catch(() => {
      message.error('获取批注失败');
      this.setState({ annotationsLoading: false }); // 加载失败也要设置状态
    });
  };

  handleAnnotationEdit = async (annotationId, commentId, content) => {
    try {
      const params ={      
          id:commentId,
          comment:content,
      } 
        
      request(`/comment/update`, {
        method: 'POST',
        body: params,   
      }).then(res => {
        if (res.code === 200) {
          // this.setState({ annotations: updatedAnnotations });
          message.success('评论更新成功');
          // this.fetchAnnotations();
          this.editorNode.sendReviewOperationChange();
        }else{
          message.error(res.msg);
        }
      }).catch(() => {
        message.error('评论更新失败');
      });
    } catch (error) {
      message.error('评论更新失败');
    }
  };

  handleAnnotationDelete = async (annotationId, commentId) => {
    try {
      request(`/comment/delete`, {
        method: 'POST',
        body: {
          id: commentId
        },
      }).then(res => {
        if (res.code === 200) {
          message.success('删除成功');
          // 节点批注-1
          this.editorNode.minder.execCommand('comment', -1);
          this.editorNode.sendReviewOperationChange();
          // this.fetchAnnotations(); // 重新获取批注列表
        } else {
          message.error(res.msg);
        }
      }).catch(() => {
        message.error('删除失败');
      });
    } catch (error) {
      message.error('删除失败');
    }
  };

  handleAnnotationClick = id => {
    this.setState({ selectedAnnotationId: id });
    // // 这里需要调用脑图组件的方法来选中对应节点
    const annotation = this.state.annotations.find(item => item.nodeId === id);
    if (annotation && this.editorNode) {
      // 调用脑图组件的选中节点方法
      this.editorNode.selectNodeById(annotation.nodeId);
    }
  };

  toggleAnnotation = () => {
    this.setState(prevState => ({
      isAnnotationVisible: !prevState.isAnnotationVisible,
    }));
  };

  handleFilterChange = (type, value) => {
    if (type === 'filterUser') {
      if (value === '所有人') {
        this.setState({ 
          filterUser: '所有人',
          filterUserName: '所有人'
        }, () => {
          this.fetchAnnotations();
        });
      } else {
        const user = this.state.userList.find(u => u.id === value);
        this.setState({ 
          filterUser: value,  // 保存ID
          filterUserName: user ? user.name : value  // 保存名字
        }, () => {
          this.fetchAnnotations();
        });
      }
    } else if (type === 'filterStatus') {
      // 处理状态筛选
      const statusName = this.statusMap[value] || value;
      this.setState({ 
        filterStatus: value,
        filterStatusName: statusName
      }, () => {
        this.fetchAnnotations();
      });
    } else {
      this.setState({ [type]: value }, () => {
        this.fetchAnnotations();
      });
    }
  };

  getFilteredAnnotations = () => {
    // 直接返回annotations，因为筛选已经在接口层完成
    return this.state.annotations;
  };
  getReviewInfo = () => {
    const {  reviewId } = this.props.location.query;
    request(`/review/getReviewById`, {
      method: 'GET',
      params: { id: reviewId },
    }).then(res => {
      if (res.code === 200) {
        this.setState({ reviewInfo: res.data });
        console.log('reviewInfo', this.state.reviewInfo);
      }
      this.setState({ productLineId: this.state.reviewInfo.productLineId });
      if (this.state.reviewInfo.productLineId) {
        this.fetchUserList();
      }
    });
  } 
  // 删除图片
  handleDeleteImage = async (imageId) => {
    try {
      const response = await request('/commentFile/delete', {
        method: 'POST',
        body: {
          id: imageId
        },
      });
      
      if (response.code === 200) {
        message.success('图片删除成功');
        this.editorNode.sendReviewOperationChange();
        // this.fetchAnnotations(); // 刷新评论列表
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      message.error('删除图片失败，请重试');
    }
  };

  // 删除附件
  handleDeleteFile = async (fileId) => {
    try {
      const response = await request('/commentFile/delete', {
        method: 'POST',
        body: {
          id: fileId
        },
      });
      
      if (response.code === 200) {
        message.success('附件删除成功');
        this.editorNode.sendReviewOperationChange();
        // this.fetchAnnotations(); // 刷新评论列表
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      message.error('删除附件失败，请重试');
    }
  };
  
  // 完成review
  handleReview =  () => {        
    const {  reviewId } = this.props.location.query;
    request(`/review/updateStatus`, {
      method: 'POST',
      body: {
        id:reviewId,
        type:1  // 1:完成review 2:完成修订
      },
    }).then(async res => {
      if (res.code === 200) {
       await this.updateCase();
        this.getReviewInfo();
        message.success('操作成功');
      }else{
        message.error(res.msg);
      }
    });
    // this.updateCase(true);
  }
  // 保存review
  updateCase = async (flag=false) => {
    const {caseId,reviewId} = this.props.location.query;
    const minderData = this.editorNode?.minder?.exportJson();
    const processedData = removeCommentCount(minderData);
    const param = {
      id: caseId,
      title: '更新内容，实际不会保存title',
      modifier: Cookie.get('userName'),
      caseContent: JSON.stringify(processedData),
      reviewType: 1,
      reviewId: reviewId,
    };
    let url = `/case/update`;
    try{  
      const res = await request(url, { method: 'POST', body: param })
      if(res.code === 200){
        // message.success('保存内容成功');
        this.editorNode?.ws?.sendMessage('review_complete_event', {reviewType:1}); // 给服务器发送review完成消息
      }else{
        message.error(res.msg);
      }
    }catch(error){
      message.error('保存内容失败');
    }
  };
  //服务端通知review完成
  handleReviewComplete = (message) => {
    Modal.confirm({
      title: '提示',
      content: '当前内容已完成Review,内容已锁定,无法继续添加批注!',
      okText: '确定',
      cancelButtonProps: { style: { display: 'none' } },
      onOk: () => {
        this.getReviewInfo();
      }
    });
  }
  render() {
    const {
      isAnnotationVisible,
      selectedAnnotationId,
      filterStatus,
      filterStatusName,
      filterUser,
      filterUserName,
      userList,
      caseId,
      reviewId,
      annotationsLoading,
      reviewInfo,
    } = this.state;
    const filteredAnnotations = this.getFilteredAnnotations();

    return (
      <div className={styles.review_container}>
        <div className={styles.header}>
          <div className={styles.left}>
            <Icon
              type="left"
              className={styles.backIcon}
              onClick={() => {
                if(reviewInfo?.companyId){
                  router.push(`/review/reviewList/${reviewInfo.companyId}`);
                }else{
                  router.push(`/`);
                }
              }}
            />
            <div className={styles.titleWrapper}>
              <span className={styles.title}>
                {reviewInfo?.status === 1 && <span style={{ color: '#1890ff', marginRight: '8px' }}>[Review中]</span>}
                {reviewInfo?.status === 2 && <span style={{ color: '#faad14', marginRight: '8px' }}>[修订中]</span>}
                {reviewInfo?.status === 3 && <span style={{ color: '#52c41a', marginRight: '8px' }}>[已完成]</span>}
                {reviewInfo?.status === 4 && <span style={{ color: '#f5222d', marginRight: '8px' }}>[已过期]</span>}
                {reviewInfo?.title}(id:{reviewInfo?.id})
              </span>
              <span className={styles.updateTime}>
                最近修改：{reviewInfo?.gmtUpdated}
              </span>
            </div>
          </div>
          <div className={styles.right}>
            <span>截止时间:{reviewInfo?.cutOffDate?reviewInfo?.cutOffDate:'无截止时间'}</span>
            {/* <span>1个review者</span> */}
            {reviewInfo?.status === 1 ? (
              <Button type="primary" onClick={this.handleReview}>我已review</Button>
            ) : (
              <Button type="primary" disabled>已完成review</Button>
            )}
          </div>
        </div>

        <div className={styles.review_content}>
          <div className={styles.mindmap}>
            <AgileTCEditor
              ref={editorNode => (this.editorNode = editorNode)}
              editorStyle={{ height: 'calc(100vh - 60px)' }}
              wsUrl={getWsUrl()}
              wsParam={{
                transports: ['websocket', 'xhr-polling', 'jsonp-polling'],
                path: '/thirdapp/ecocase/ws/api/socket.io',
                query: {
                  caseId: caseId,
                  recordId: undefined,
                  reviewId: reviewId,
                  user: Cookie.get('userName')||'未知',
                },
              }}
              readOnly={true}
              type="review"
              onNodeSelect={this.handleNodeSelect}
              commentUrl='/comment/insert'
              reviewId={reviewId}
              getCommentList={this.fetchAnnotations}
              onReviewChange={this.fetchAnnotations}
              reviewStatus={reviewInfo?.status}
              onReviewComplete={this.handleReviewComplete}
            />
          </div>
          <div className={styles.annotationContainer}>
            <div className={styles.collapseBtn} onClick={this.toggleAnnotation}>
              <Icon type={isAnnotationVisible ? 'right' : 'left'} />
            </div>
            <div
              className={`${styles.annotation} ${
                !isAnnotationVisible ? styles.collapsed : ''
              }`}
            >
              <div className={styles.annotationHeader}>
                <div className={styles.title}>批注列表</div>
                <div className={styles.filter}>
                  <Dropdown
                    overlay={
                      <Menu
                        selectedKeys={[filterUser === '所有人' ? '所有人' : userList.find(u => u.id === filterUser)?.name || filterUser]}
                        onClick={({ key }) =>
                          this.handleFilterChange('filterUser', key === '所有人' ? '所有人' : userList.find(u => u.name === key)?.id || key)
                        }
                      >
                        <Menu.Item key="所有人">所有人</Menu.Item>
                        {userList.map(user => (
                          <Menu.Item key={user.name}>{user.name}</Menu.Item>
                        ))}
                      </Menu>
                    }
                    trigger={['click']}
                  >
                    <span className={styles.filterDropdown}>
                      {filterUserName} <Icon type="down" />
                    </span>
                  </Dropdown>
                  <Dropdown
                    overlay={
                      <Menu
                        selectedKeys={[filterStatus]}
                        onClick={({ key }) =>
                          this.handleFilterChange('filterStatus', key)
                        }
                      >
                        <Menu.Item key="全部">全部</Menu.Item>
                        <Menu.Item key="1">未处理</Menu.Item>
                        <Menu.Item key="2">同意</Menu.Item>
                        <Menu.Item key="3">忽略</Menu.Item>
                      </Menu>
                    }
                    trigger={['click']}
                  >
                    <span className={styles.filterDropdown}>
                      {filterStatusName} <Icon type="down" />
                    </span>
                  </Dropdown>
                </div>
              </div>
              <div className={styles.annotationList}>
                <Spin spinning={annotationsLoading}>
                  {filteredAnnotations.length > 0 ? (
                    filteredAnnotations.map(annotation => (
                      <AnnotationItem
                        key={annotation.id}
                        data={annotation}
                        onEdit={this.handleAnnotationEdit}
                        onDelete={this.handleAnnotationDelete}
                        isSelected={selectedAnnotationId === annotation.nodeId}
                        onClick={this.handleAnnotationClick}
                        onReply={this.handleAnnotationReply}
                        onReplyEdit={this.handleReplyEdit}
                        onReplyDelete={this.handleReplyDelete}
                        pageType="review"
                        minder={this.editorNode.minder}
                        fetchAnnotations={this.fetchAnnotations}
                        onDeleteImage={this.handleDeleteImage}
                        onDeleteFile={this.handleDeleteFile}
                      />
                    ))
                  ) : (
                    <div className={styles.emptyState}>
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description="暂无批注"
                      />
                    </div>
                  )}
                </Spin>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default ReviewPage;
