.layout {
    min-height: 100vh;
    margin-left: 80px;

  }
  
  .sider {
    position: fixed !important;
    left: 0;
    height: 100vh;
    overflow: auto;
    
    .logo {
      height: 64px;
      line-height: 64px;
      text-align: center;
      color: #fff;
      font-size: 20px;
      font-weight: bold;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
  }
  
  .content {
    // margin-left: 200px;
    // padding: 24px;
    // min-height: 100vh;
    // background: #f0f2f5;
  }
//   .ant-layout{
//     margin-left: 80px;
//   }
.userInfoWrapper {
  position: absolute;
  bottom: 55px;
  left: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  
  .userInfoIcon {
    width: 24px;
    height: 24px;
  }
}

.userPopover {
  padding: 8px;
  
  .username {
    margin-bottom: 8px;
    font-size: 14px;
    padding: 4px 0;
  }
  
  .menuItem {
    color: #1890ff;
    cursor: pointer;
    padding: 4px 0;
    
    &:hover {
      text-decoration: underline;
    }
  }
  
  .logoutBtn {
    color: #1890ff;
    cursor: pointer;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.menuIcon {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 6px;
    right: -2px;
    width: 6px;
    height: 6px;
    background-color: #f5222d;
    border-radius: 50%;
    display: none;
  }

  &.hasReview::after {
    display: block;
  }
}