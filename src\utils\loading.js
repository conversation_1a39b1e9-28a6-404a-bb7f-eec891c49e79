// import { nextTick } from 'vue';
import './loading.scss';

/**
 * 页面全局 Loading
 * @method start 创建 loading
 * @method done 移除 loading
 */
export const NextLoading = {
 // 创建 loading
 start: () => {
    // 防止重复创建
    if (document.querySelector('.loading-next')) return;
    
    const div = document.createElement('div');
    div.setAttribute('class', 'loading-next');
    const htmls = `
      <div class="loading-next-box">
        <div class="loading-next-box-warp">
          <div class="loading-next-box-item"></div>
          <div class="loading-next-box-item"></div>
          <div class="loading-next-box-item"></div>
          <div class="loading-next-box-item"></div>
          <div class="loading-next-box-item"></div>
          <div class="loading-next-box-item"></div>
          <div class="loading-next-box-item"></div>
          <div class="loading-next-box-item"></div>
          <div class="loading-next-box-item"></div>
        </div>
      </div>
    `;
    div.innerHTML = htmls;
    document.body.appendChild(div);  // 改用 appendChild
    window.nextLoading = true;
  },
  
  // 移除 loading
  done: (time = 0) => {
    setTimeout(() => {
      window.nextLoading = false;
      const el = document.querySelector('.loading-next');
      if (el) {
        el.parentNode.removeChild(el);
      }
    }, time);
  },
};
