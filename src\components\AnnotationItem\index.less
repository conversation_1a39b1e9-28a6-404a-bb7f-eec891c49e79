.CommentGroup {
    background-color: #fff;
    width: 100%;
    background: #fff;
    box-shadow: 0 1px 4px 1px rgba(185, 179, 179, .3);
    border-radius: 4px;
    margin-bottom: 10px;
    overflow: hidden;
    position: relative;
    .nodeInfo {
        padding: 12px 16px;
        // border-bottom: 1px solid #e8e8e8;
        // background: #fafafa;
        .left {
            display: flex;
            align-items: center;
            
            .count {
                background: #d4d8de;
                border-radius: 18px;
                font-weight: 400;
                font-size: 12px;
                line-height: 17px;
                color: #fff;
                min-width: 24px;
                padding: 0 4px;
                display: inline-block;
                text-align: center;
                margin-right: 3px;
            }

            .nodeTitle {
                color: #333;
                font-size: 14px;
                display: inline-block;
                max-width: 260px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .ellipse-1 {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .commentContent {
        padding: 0 16px 16px 16px;

        .commentHeader {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .userInfo {
                .username {
                    font-size: 14px;
                    color: #333;
                    font-weight: 600;
                }
                .time {
                    font-size: 12px;
                    color: #999;
                    margin: 0 10px;
                }
            }
            
            .actionIcon {
                color: #999;
                font-size: 16px;
                cursor: pointer;
                &:hover {
                    color: #1890ff;
                }
            }

            .rightInfo {
                .status {
                    font-size: 12px;
                    color: #999;
                    &.statusAgree {
                        color: #52c41a;
                    }
                    &.statusIgnore {
                        color: #ff4d4f;
                    }
                }
                .actionButtons {
                    display: flex;
                    gap: 8px;

                    .actionButton {
                        font-size: 12px;
                        padding: 0 8px;
                        height: 24px;
                        border-radius: 2px;
                        &.agreeButton {
                            &.active {
                                background-color: #52c41a;
                                border-color: #52c41a;
                                color: white;
                            }
                        }
                        &.ignoreButton {
                            &.active {
                                background-color: #ff4d4f;
                                border-color: #ff4d4f;
                                color: white;
                            }
                        }
                    }
                }
            }
        }

        .commentBody {
            margin-bottom: 8px;
            .textarea {
                width: 100%;
                border: 1px solid transparent;
                border-radius: 4px;
                padding: 8px;
                background-color: #f9f9f9;
                font-size: 14px;
                color: #333;
                &:hover, &:focus {
                    border-color: #d9d9d9;
                }
                &:disabled {
                    background-color: #f9f9f9;
                    color: #333;
                    border-color: transparent;
                    cursor: default;
                }
                &.editing {
                    background-color: #fff;
                    border-color: #40a9ff;
                    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                    &:hover, &:focus {
                        border-color: #40a9ff;
                    }
                }
            }
        }

        // 展开/收起按钮样式
        .expandButton {
          width: 100%;
          text-align: center;
          color: #1890ff;
          cursor: pointer;
          font-size: 12px;
          padding: 4px 0;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s;
          margin-bottom: 12px;

          &:hover {
            color: #40a9ff;
            background-color: #f0f5ff;
            border-radius: 4px;
          }

          .anticon {
            margin-right: 4px;
            font-size: 12px;
          }

          .expandText {
            font-size: 12px;
          }
        }

        // 图片容器样式
        .imageContainer {
          display: grid;
          grid-template-columns: repeat(4, 80px);
          gap: 8px;
          margin-bottom: 8px;
          width: fit-content;
        }

        // 图片包装器样式
        .imageWrapper {
            position: relative;
            width: 80px;
            height: 80px;
            cursor: pointer;
            border-radius: 4px;
            overflow: hidden;
            
            &:hover .deleteButton {
                opacity: 1;
                visibility: visible;
            }
        }

        // 评论图片样式
        .commentImage {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
            
            &:hover {
                transform: scale(1.05);
            }
        }

        // 删除按钮样式
        .deleteButton {
            position: absolute;
            top: 0;
            right: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 0 4px 0 4px;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
            z-index: 10;
            
            &:hover {
                background: rgba(255, 255, 255, 0.95);
            }
            
            i {
                color: #ff4d4f;
                font-size: 14px;
            }
        }

        // 附件容器样式
        .fileContainer {
          margin-top: 8px;
          margin-bottom: 12px;
          border-top: 1px dashed #e8e8e8;
          padding-top: 8px;
        }

        // 附件项样式
        .fileItem {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            padding: 4px 8px;
            background: #f9f9f9;
            border-radius: 4px;
            
            &:hover {
                background: #f0f0f0;
            }
        }

        // 附件图标样式
        .fileIcon {
            margin-right: 8px;
            font-size: 16px;
            color: #1890ff;
        }

        // 附件名称样式
        .fileName {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #333;
            font-size: 13px;
        }

        // 附件操作按钮容器
        .fileActions {
            display: flex;
            align-items: center;
        }

        // 附件操作按钮
        .fileAction {
            margin-left: 12px;
            cursor: pointer;
            color: #999;
            
            &:hover {
                color: #1890ff;
            }
        }

        .replyInput {
            margin-bottom: 12px;
            textarea {
                width: 100%;
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                resize: none;
                &:focus {
                    border-color: #40a9ff;
                    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                }
            }
        }

        .replyContent {
            margin-top: 8px;
            padding: 8px 12px;
            background-color: #f6f6f6;
            border-radius: 4px;

            .replyHeader {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;

                .username {
                    font-size: 13px;
                    font-weight: bold;
                    color: #333;
                }

                .time {
                    font-size: 12px;
                    color: #999;
                    margin-left: 8px;
                }
            }

            .replyBody {
                font-size: 13px;
                color: #333;
                line-height: 1.6;
                margin-top: 4px;
            }
        }
    }

    &.active {
        border-left: 3px solid #1890ff;
        .decorate_div {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: #1890ff;
            display: block;
        }
    }

    .decorate_div {
        display: none;
    }
}

.replyContent {
  margin-left: 20px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-top: 8px;

  .replyHeader {
    display: flex;
    align-items: center;
    margin-bottom: 4px;

    .username {
      font-weight: 500;
      margin-right: 8px;
    }

    .time {
      color: #8c8c8c;
      font-size: 12px;
      margin-right: 10px;
    }
  }

  .replyBody {
    color: #333;
    font-size: 14px;
    line-height: 1.5;
  }
}

.replyInput {
  margin-left: 20px;
  margin-top: 8px;

  :global {
    .ant-input {
      border-radius: 4px;
      resize: none;
      
      &:focus {
        box-shadow: none;
        border-color: #40a9ff;
      }
    }
  }
}