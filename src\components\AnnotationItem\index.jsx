import React, { useState, useEffect, useRef } from 'react';
import { Dropdown, Menu, Modal, Input, message, Icon, Tooltip, Button, Tag } from 'antd';
import styles from './index.less';
import classNames from 'classnames';
import request from '@/utils/axios';
const AnnotationItem = ({ 
  data, 
  onEdit, 
  onDelete, 
  onReply,
  onReplyEdit,
  onReplyDelete,
  isSelected,
  onClick,
  pageType = 'review', // 新增pageType属性，默认为review
  minder,
  fetchAnnotations,
  reviewStatus,
  onDeleteImage,
  onAnnotationStatus,
  onDeleteFile // 新增删除附件的回调函数
}) => {
  const [editingCommentId, setEditingCommentId] = useState(null);
  const [editContent, setEditContent] = useState('');
  const [replyingToId, setReplyingToId] = useState(null);
  const [replyContent, setReplyContent] = useState('');
  const [editingReplyId, setEditingReplyId] = useState(null);
  const [editReplyContent, setEditReplyContent] = useState('');
  const [expandedComments, setExpandedComments] = useState(new Set());
  const [lastSelectedNode, setLastSelectedNode] = useState(null);
  const [commentStatuses, setCommentStatuses] = useState({});  // 修改：用于存储评论状态值
  const [lastClickTime, setLastClickTime] = useState({}); // 新增：用于存储每个评论的最后点击时间
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [expandedImages, setExpandedImages] = useState({});
  const [expandedFiles, setExpandedFiles] = useState({});
  const [buttonStates, setButtonStates] = useState({}); // 新增：用于跟踪按钮状态
  const editTextAreaRef = useRef(null);

  // 添加初始化评论状态的useEffect
  useEffect(() => {
    // 初始化评论状态
    const initialStatuses = {};
    data.comments.forEach(comment => {
      if (comment.status) {
        initialStatuses[comment.id] = comment.status;
      }
    });
    setCommentStatuses(initialStatuses);
  }, [data]);

  // 初始化按钮状态
  useEffect(() => {
    // 从评论数据初始化按钮状态
    const initialButtonStates = {};
    data.comments.forEach(comment => {
      initialButtonStates[comment.id] = comment.status || 1;
    });
    setButtonStates(initialButtonStates);
    // sessionStorage.setItem('初始化按钮状态data', JSON.stringify(data.comments));
    // sessionStorage.setItem('初始化按钮状态', JSON.stringify(initialButtonStates));
    // console.log('初始化按钮状态', JSON.stringify(initialButtonStates));
  }, [data]);

  // 监听编辑状态,当进入编辑状态时记录并取消选中节点,退出时恢复
  useEffect(() => {
    if (editingCommentId || replyingToId || editingReplyId) {
      // 进入编辑状态
      const selectedNode = minder.getSelectedNode(); 
      if (selectedNode) {
        setLastSelectedNode(selectedNode);
        minder.removeAllSelectedNodes();
      }
    } else {
      // 退出编辑状态
      if (lastSelectedNode) {      
        // minder.enable();
        // minder.select(lastSelectedNode, true);
        // minder.setStatus('normal', true);
        // setLastSelectedNode(null);
      }
    }
  }, [editingCommentId, replyingToId, editingReplyId, minder]);

  // 处理按钮点击
  const handleStatusClick = async (commentId, targetStatus, currentStatus,comment) => {
    const now = Date.now();
    const lastClick = lastClickTime[commentId] || 0;
    const DEBOUNCE_TIME = 1000; // 防抖时间1000ms
    // sessionStorage.setItem('点击按钮data数据', JSON.stringify(data.comments));
    // 如果在防抖时间内，直接返回
    if (now - lastClick < DEBOUNCE_TIME) {
      message.warning('操作太频繁，请稍后再试');
      return;
    }

    // 更新最后点击时间
    setLastClickTime(prev => ({
      ...prev,
      [commentId]: now
    }));
    
    // 判断新状态：如果当前状态与目标状态相同，则重置为未处理状态(1)，否则设置为目标状态
    const newStatus = currentStatus === targetStatus ? 1 : targetStatus;
    // if(comment){
    //   comment.status = newStatus;
    // }
    
    // 更新本地状态，实现即时UI响应
    // 立即更新按钮状态以更新UI
    setButtonStates(prev => ({
      ...prev,
      [commentId]: newStatus
    }));
    
    // 同时更新评论状态缓存，用于显示状态名称
    setCommentStatuses(prev => ({
      ...prev,
      [commentId]: newStatus
    }));

    try {
      console.log('正在调用状态更新接口，参数:', {
        id: commentId,
        status: newStatus,
        url: '/comment/updateStatus'
      });
      
      // 调用接口更新状态
      const response = await request('/comment/updateStatus', {
        method: 'POST',
        body: JSON.stringify({
          id: commentId,
          status: newStatus
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (response.code !== 200) {
        console.error('更新状态失败，返回结果:', response);
        throw new Error(response.msg || '更新状态失败');
      }

      message.success(newStatus === 1 ? '已重置为未处理' : `${newStatus === 2 ? '同意' : '忽略'}成功`);
      
      // 更新状态后的回调
      if(onAnnotationStatus){
        onAnnotationStatus();
      }else if(fetchAnnotations){
        fetchAnnotations();
      }
    } catch (error) {
      console.error('状态更新出错:', error);
      message.error(`操作失败: ${error.message || '请重试'}`);
      
      // 出错时恢复原按钮状态
      setButtonStates(prev => ({
        ...prev,
        [commentId]: currentStatus
      }));
      
      // 同时恢复评论状态缓存
      setCommentStatuses(prev => ({
        ...prev,
        [commentId]: currentStatus
      }));
    }
  };

  const handleMenuClick = (commentId) => ({ key }) => {
    if (key === 'edit') {
      const comment = data.comments.find(c => c.id === commentId);
      setEditContent(comment.content);
      setEditingCommentId(commentId);
    } else if (key === 'delete') {
      Modal.confirm({
        title: '确认删除',
        content: '确定要删除这条批注吗？',
        onOk: () => onDelete(data.id, commentId),
      });
    } else if (key === 'reply') {
      setReplyingToId(commentId);
      setReplyContent('');
    }
  };

  const handleKeyDown = (type, commentId, replyId = null) => async (e) => {
    // 确保焦点在输入框内
    e.target.focus();
    
    // 如果按下了 shift 键,允许换行
    if (e.key === 'Enter' && !e.shiftKey) {
      // 立即阻止默认行为和冒泡
      e.preventDefault();
      e.stopPropagation();
      
      try {
        if (type === 'reply') {
          if (!replyContent.trim()) {
            message.error('回复内容不能为空');
            return;
          }
          await onReply(data.id, commentId, replyContent);
          setReplyingToId(null);
          setReplyContent('');
        } else if (type === 'edit') {
          if (!editContent.trim()) {
            message.error('批注内容不能为空');
            return;
          }
          // 先更新本地状态，使UI立即响应
          const updatedComments = data.comments.map(c => 
            c.id === commentId ? { ...c, content: editContent } : c
          );
          data.comments = updatedComments;
          
          // 只传递内容参数，保持与现有API兼容
          await onEdit(data.id, commentId, editContent);
          setEditingCommentId(null);
          setEditContent('');
        } else if (type === 'replyEdit') {
          if (!editReplyContent.trim()) {
            message.error('回复内容不能为空');
            return;
          }
          await onReplyEdit(replyId, commentId, editReplyContent);
          setEditingReplyId(null);
          setEditReplyContent('');
        }
        if (lastSelectedNode) {
          setTimeout(()=>{
            minder.select(lastSelectedNode, true);
            setLastSelectedNode(null);
          },0)
        }
      } catch (error) {
        // 如果接口调用失败，不清除编辑状态
        message.error('操作失败，请重试');
      }
    }
  };

  //处理函数用于处理输入框的键盘事件
  const handleTextAreaKeyDown = (e) => {  
    // 阻止所有键盘事件的冒泡
    e.stopPropagation();
  };

  const handleReplyEdit = (commentId, replyId) => {
    setEditingReplyId(replyId);
    const comment = data.comments.find(c => c.id === commentId);
    const reply = comment.replies.find(r => r.id === replyId);
    setEditReplyContent(reply.content);
  };

  const handleReplyDelete = (commentId, replyId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条回复吗？',
      onOk: () => {
        onReplyDelete(replyId);
      },
    });
  };

  // 图片预览功能
  const handlePreview = (url) => {
    setPreviewImage(url);
    setPreviewVisible(true);
  };

  const getReplyMenu = (commentId, replyId) => {
    if (pageType === 'contrast') {
      return null; // contrast模式下不显示任何操作按钮
    }

    // 获取当前用户信息
    const userInfo = sessionStorage.getItem('userInfo');
    const currentUser = userInfo ? JSON.parse(userInfo) : null;
    
    // 获取当前回复
    const currentComment = data.comments.find(c => c.id === commentId);
    const currentReply = currentComment?.replies?.find(r => r.id === replyId);
    
    // 检查是否是回复作者
    const canEdit = currentUser && currentReply && currentUser.username === currentReply.username;
    
    return (
      <Menu onClick={({ key }) => {
        if (key === 'edit') {
          handleReplyEdit(commentId, replyId);
        } else if (key === 'delete') {
          handleReplyDelete(commentId, replyId);
        }
      }}>
        {canEdit && <Menu.Item key="edit">编辑</Menu.Item>}
        <Menu.Item key="delete" style={{color:'red'}}>删除</Menu.Item>
      </Menu>
    );
  };

  const getMenu = (commentId) => {
    if (pageType === 'contrast') {
      return null; // contrast模式下不显示任何操作按钮
    }

    // 获取当前用户信息
    const userInfo = sessionStorage.getItem('userInfo');
    const currentUser = userInfo ? JSON.parse(userInfo) : null;
    
    // 获取当前评论
    const currentComment = data.comments.find(c => c.id === commentId);
    
    if (pageType === 'review') {
      // 在review模式下，只有评论作者才能编辑自己的评论
      const canEdit = currentUser && currentComment && currentUser.username === currentComment.username;
      return (
        <Menu onClick={handleMenuClick(commentId)}>
          {canEdit && <Menu.Item key="edit">编辑</Menu.Item>}
          <Menu.Item key="delete" style={{color:'red'}}>删除</Menu.Item>
        </Menu>
      );
    } else {
      return (
        <Menu onClick={handleMenuClick(commentId)}>
          <Menu.Item key="reply">评论</Menu.Item>
          {/* <Menu.Item key="delete" style={{color:'red'}}>删除</Menu.Item> */}
        </Menu>
      );
    }
  };

  const toggleReplies = (commentId) => {
    setExpandedComments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(commentId)) {
        newSet.delete(commentId);
      } else {
        newSet.add(commentId);
      }
      return newSet;
    });
  };

  const renderReplies = (comment) => {
    const isExpanded = expandedComments.has(comment.id);
    const replies = comment.replies || [];
    const displayReplies = isExpanded ? replies : replies.slice(0, 2);
    const hasMore = replies.length > 2;

    return (
      <>
        {displayReplies.map(reply => (
          <div key={reply.id} className={styles.replyContent} onClick={e => e.stopPropagation()}>
            <div className={styles.replyHeader}>
              <span className={styles.username}>{reply.username}</span>
              <span className={styles.time}>{reply.time}</span>
              {pageType !== 'contrast' && (
                <Dropdown
                  overlay={getReplyMenu(comment.id, reply.id)}
                  trigger={['click']}
                  placement="bottomRight"
                >
                  <Icon type="ellipsis" className={styles.actionIcon} />
                </Dropdown>
              )}
            </div>
            <div className={styles.replyBody}>
              {editingReplyId === reply.id ? (
                <Input.TextArea
                  value={editReplyContent}
                  onChange={(e) => setEditReplyContent(e.target.value)}
                  autoSize={{ minRows: 1 }}
                  onKeyDown={handleKeyDown('replyEdit', comment.id, reply.id)}
                  onBlur={() => {
                    setEditingReplyId(null);
                    setEditReplyContent('');
                  }}
                  onClick={e => e.stopPropagation()}
                />
              ) : (
                reply.content
              )}
            </div>
          </div>
        ))}
        
        {hasMore && (
          <div 
            className={styles.expandButton}
            onClick={() => toggleReplies(comment.id)}
          >
            <Tooltip title={isExpanded ? "收起回复" : "展开全部回复"}>
              <Icon type={isExpanded ? "up" : "down"} />
              <span className={styles.expandText}>
                {isExpanded ? "收起" : `展开其他 ${replies.length - 2} 条回复`}
              </span>
            </Tooltip>
          </div>
        )}
      </>
    );
  };

  // 渲染评论图片
  const renderImages = (comment) => {
    if (!comment.images || comment.images.length === 0) return null;
    
    const isExpanded = expandedImages[comment.id] || false;
    const defaultShowCount = 4;
    const displayImages = isExpanded ? comment.images : comment.images.slice(0, defaultShowCount);
    const hasMore = comment.images.length > defaultShowCount;
    
    return (
      <div>
        <div className={styles.imageContainer}>
          {displayImages.map((item, index) => (
            <div 
              key={`${comment.id}-img-${index}`}
              className={styles.imageWrapper}
            >
              <img 
                src={item.url} 
                alt=""
                className={styles.commentImage}
                onClick={(e) => {
                  e.stopPropagation();
                  handlePreview(item.url);
                }}
              />
              {pageType === 'review' && (
                <div className={styles.deleteButton}>
                  <Icon
                    type="delete"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteImage && onDeleteImage(item.id);
                    }}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
        {hasMore && (
          <div 
            className={styles.expandButton}
            onClick={() => {
              setExpandedImages(prev => ({
                ...prev,
                [comment.id]: !isExpanded
              }));
            }}
          >
            <Tooltip title={isExpanded ? "收起图片" : "展开更多图片"}>
              <Icon type={isExpanded ? "up" : "down"} />
              <span className={styles.expandText}>
                {isExpanded ? "收起" : `展开剩余 ${comment.images.length - defaultShowCount} 张图片`}
              </span>
            </Tooltip>
          </div>
        )}
      </div>
    );
  };

  // 渲染附件函数
  const renderFiles = (comment) => {
    if (!comment.files || comment.files.length === 0) return null;
    
    const isExpanded = expandedFiles[comment.id] || false;
    const defaultShowCount = 1;
    const displayFiles = isExpanded ? comment.files : comment.files.slice(0, defaultShowCount);
    const hasMore = comment.files.length > defaultShowCount;
    
    return (
      <div className={styles.fileContainer}>
        {displayFiles.map((file, index) => (
          <div 
            key={`${comment.id}-file-${index}`}
            className={styles.fileItem}
          >
            <Icon type={getFileIcon(file.name)} className={styles.fileIcon} />
            <span className={styles.fileName}>{file.name}</span>
            <div className={styles.fileActions}>
              <Tooltip title="下载">
                <Icon 
                  type="download" 
                  className={styles.fileAction} 
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(file.url);
                  }}
                />
              </Tooltip>
              {pageType === 'review' && (
                <Tooltip title="删除">
                  <Icon
                    type="delete"
                    className={styles.fileAction}
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteFile && onDeleteFile(file.id);
                    }}
                  />
                </Tooltip>
              )}
            </div>
          </div>
        ))}
        {hasMore && (
          <div 
            className={styles.expandButton}
            onClick={() => {
              setExpandedFiles(prev => ({
                ...prev,
                [comment.id]: !isExpanded
              }));
            }}
          >
            <Tooltip title={isExpanded ? "收起附件" : "展开更多附件"}>
              <Icon type={isExpanded ? "up" : "down"} />
              <span className={styles.expandText}>
                {isExpanded ? "收起" : `展开剩余 ${comment.files.length - defaultShowCount} 个附件`}
              </span>
            </Tooltip>
          </div>
        )}
      </div>
    );
  };

  // 根据文件名获取对应的图标类型
  const getFileIcon = (fileName) => {
    const ext = fileName.split('.').pop().toLowerCase();
    
    if (['doc', 'docx'].includes(ext)) return 'file-word';
    if (['xls', 'xlsx'].includes(ext)) return 'file-excel';
    if (['ppt', 'pptx'].includes(ext)) return 'file-ppt';
    if (['pdf'].includes(ext)) return 'file-pdf';
    if (['zip', 'rar', '7z'].includes(ext)) return 'file-zip';
    if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) return 'file-image';
    
    return 'file';
  };

  return (
    <div 
      id={`annotation-${data.nodeId}`}
      className={classNames(styles.CommentGroup, {
        [styles.active]: isSelected
      })}
      onClick={(e) => {
        e.stopPropagation();       
        if(data?.nodeStatus === 1&&pageType!=='contrast'){
          message.warning('节点已被删除!');
          return;
        }
        onClick(data.nodeId);
      }}
    >
      <div className={classNames(styles.decorate_div,{
        [styles.active]: isSelected
      })} />
      <div className={styles.nodeInfo}>
        <div className={styles.left}>
          <span className={styles.count}>{data.comments.length}</span>
          <span className={styles.nodeTitle}>{data.nodeTitle}</span>
          <span>节点</span>
          {data?.nodeStatus === 1 && <Tag color="red" style={{marginLeft: '8px'}}>已被删除</Tag>}
        </div>
      </div>
      
      {data.comments.map(comment => (
        <div key={comment.id} className={styles.commentContent} onClick={(e) => {
          // 如果正在回复，点击评论区域时取消回复
          if (replyingToId === comment.id) {
            setReplyingToId(null);
            setReplyContent('');
          }
        }}>
          <div className={styles.commentHeader}>
            <div className={styles.userInfo}>
              <span className={styles.username}>{comment.username}</span>
              <span className={styles.time}>{comment.time}</span>
              {pageType !== 'contrast' && (
                <Dropdown 
                  overlay={getMenu(comment.id)} 
                  trigger={['click']} 
                  placement="bottomRight"
                  overlayClassName={styles.actionDropdown}
                >
                  <Icon type="ellipsis" className={styles.actionIcon} />
                </Dropdown>
              )}
            </div>
            <div className={styles.rightInfo}>
              {pageType === 'revise' ? (
                <div className={styles.actionButtons}>
                  <Button
                    size="small"
                    className={classNames(styles.actionButton, styles.agreeButton, {
                      [styles.active]: buttonStates[comment.id] === 2
                    })}
                    onClick={(e) => {
                      e.stopPropagation();
                      // 使用buttonStates获取当前状态
                      const currentStatus = buttonStates[comment.id] || 1;
                      handleStatusClick(comment.id, 2, currentStatus,comment);
                    }}
                    disabled={reviewStatus !== 2}
                  >
                    同意
                  </Button>
                  <Button
                    size="small"
                    className={classNames(styles.actionButton, styles.ignoreButton, {
                      [styles.active]: buttonStates[comment.id] === 3
                    })}
                    onClick={(e) => {
                      e.stopPropagation();
                      // 使用buttonStates获取当前状态
                      const currentStatus = buttonStates[comment.id] || 1;
                      handleStatusClick(comment.id, 3, currentStatus,comment);
                    }}
                    disabled={reviewStatus !== 2}
                  >
                    忽略
                  </Button>
                </div>
              ) : (
                <span className={classNames(styles.status, {
                  [styles.statusAgree]: buttonStates[comment.id] === 2 || comment.status === 2,
                  [styles.statusIgnore]: buttonStates[comment.id] === 3 || comment.status === 3
                })}>
                  {(() => {
                    // 优先使用本地状态
                    const status = buttonStates[comment.id] || comment.status;
                    switch(status) {
                      case 2: return '同意';
                      case 3: return '忽略';
                      default: return '未处理';
                    }
                  })()}
                </span>
              )}
            </div>
          </div>
          <div className={styles.commentBody}>
            {editingCommentId === comment.id ? (
              <Input.TextArea
                ref={input => {
                  if (input) {
                    input.focus();
                    const textArea = input.textarea;
                    if (textArea) {
                      textArea.selectionStart = editContent.length;
                      textArea.selectionEnd = editContent.length;
                    }
                  }
                }}
                key={`${comment.id}-edit`}
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className={classNames(styles.textarea, styles.editing)}
                autoSize={{ minRows: 1, maxRows: 5 }}
                onKeyDown={(e) => {
                  handleTextAreaKeyDown(e);
                  handleKeyDown('edit', comment.id)(e);
                }}
                onBlur={() => {
                  setEditingCommentId(null);
                  setEditContent('');
                }}
                onClick={e => e.stopPropagation()}
              />
            ) : (
              <Input.TextArea
                key={`${comment.id}-read`}
                value={comment.content}
                readOnly
                disabled
                className={styles.textarea}
                autoSize={{ minRows: 1, maxRows: 5 }}
                onClick={e => e.stopPropagation()}
              />
            )}
          </div>
          
          {/* 评论图片区域 */}
          {renderImages(comment)}
          
          {/* 附件区域 */}
          {renderFiles(comment)}
          
          {/* 回复输入框 */}
          {replyingToId === comment.id && (
            <div className={styles.replyInput}>
              <Input.TextArea
                ref={input => {
                  if (input) {
                    input.focus();
                  }
                }}
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder="请输入回复内容 按回车确认"
                autoSize={{ minRows: 1, maxRows: 5 }}
                onKeyDown={(e) => {
                  handleTextAreaKeyDown(e);
                  handleKeyDown('reply', comment.id)(e);
                }}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            </div>
          )}

           {/* 回复列表 */}
           {comment.replies && comment.replies.length > 0 && renderReplies(comment)}
        </div>
      ))}

      {/* 图片预览模态框 */}
      <Modal
        visible={previewVisible}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={800}
        bodyStyle={{ 
          padding: '12px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px',
          background: '#f0f0f0'
        }}
      >
        <div style={{
          maxWidth: '100%',
          maxHeight: '80vh',
          overflow: 'auto',
          textAlign: 'center'
        }}>
          <img
            alt="预览图片"
            style={{ 
              maxWidth: '100%',
              maxHeight: '80vh',
              objectFit: 'contain'
            }}
            src={previewImage}
          />
        </div>
      </Modal>
    </div>
  );
};

export default AnnotationItem;