import React from 'react';
import { Row, Col, Button, Icon, Table, Tabs, Tooltip, Modal, message, Tag } from 'antd';
import request from '@/utils/axios';
import moment from 'moment';
import ReviewModal from './ReviewModal';
import ReviewFilterDrawer from './ReviewFilterDrawer';
import router from 'umi/router';
import './ReviewList.scss';

const { TabPane } = Tabs;

// 添加状态常量
const STATUS_MAP = {
  1: { text: 'Review中', color: '#1890ff' },
  2: { text: '修订中', color: '#faad14' },
  3: { text: '完成', color: '#52c41a' },
  4: { text: '过期', color: '#ff4d4f' }
};

class ReviewList extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      list: [],
      total: 0,
      current: 1,
      pageSize: 10,
      loading: true,
      activeTab: 'all',
      companyName: '',
      filterVisible: false,
      filterStatus: 'filter-hide',
      modalVisible: false,
      modalTitle: '',
      currentRecord: null,
      productLineId: this.props.match.params.productLineId || undefined,
      filterReviewers: undefined,
      filterModifiers: undefined,
      filterStatusValue: undefined,
      filterTitle: '',
      reviewerOptions: [],
      modifierOptions: [],
      currentUser: null  // 添加当前用户信息
    };
  }

  componentDidMount() {
    this.getCurrentUser();
    this.getCompanyInfo();
    this.getReviewList();
    // this.getFilterOptions();
  }

  // 获取当前登录用户信息
  getCurrentUser = () => {
    request('/user/getCurrentUser', {
      method: 'GET'
    }).then(res => {
      if (res.code === 200) {
        this.setState({
          currentUser: res.data
        });
      }
    });
  };

  getCompanyInfo = () => {
    const { productLineId } = this.props.match.params;
    request(`/company/get`, {
      method: 'GET',
      params: {
        companyId: productLineId,
      },
    }).then(res => {
      if (res.code === 200) {
        this.setState({
          companyName: res.data.name,
        });
      }
    });
  };

  // getFilterOptions = () => {
  //   const mockUsers = [
  //     { value: 'user1', label: '用户1' },
  //     { value: 'user2', label: '用户2' },
  //     { value: 'user3', label: '用户3' }
  //   ];

  //   this.setState({
  //     reviewerOptions: mockUsers,
  //     modifierOptions: mockUsers
  //   });
  // };
  // 获取Review列表
  getReviewList = (page = 1) => {
    const { activeTab, filterReviewers, filterModifiers, filterTitle, filterStatusValue,pageSize,current } = this.state;
    const { productLineId } = this.props.match.params;
    
    this.setState({ loading: true });

    const params = {
      companyId: this.props.match.params.productLineId,
      pageNum: page,
      pageSize: pageSize,
      channel:1,
      productLineId:productLineId,
      relation:activeTab==='all'?false:true
    }
    if(filterReviewers){
      params.review=filterReviewers;
    }
    if(filterModifiers){
      params.revise=filterModifiers;
    }
    if(filterStatusValue){  
      params.status=filterStatusValue;
    }
    if(filterTitle){
      params.title=filterTitle;
    }
    request(`/review/queryByPage`, {
      method: 'GET',
        params:params
    }).then(res => {
      if (res.code === 200) {
       res.data.dataSources.forEach(item=>{
        item.reviewer=item.reviewIds?.map(item=>item.username).join(',') || '';
        item.modifier=item.reviseIds?.map(item=>item.username).join(',') || '';
       })
       this.setState({
        list:res.data.dataSources,
        total: res.data.total,
        loading: false
       })
      }
    });
  };

  handleTabChange = (activeKey) => {
    this.setState({ 
      activeTab: activeKey,
      current: 1
    }, () => {
      this.getReviewList(1);
    });
  };

  filterHandler = () => {
    this.setState({ 
      filterVisible: true,
      filterStatus: 'filter-show' 
    });
  };

  closeFilter = () => {
    this.setState({
      filterVisible: false,
      filterStatus: 'filter-hide'
    });
  };

  handlePageChange = (page, pageSize) => {
    this.setState({
      current: page,
      pageSize
    }, () => {
      this.getReviewList(page);
    });
  };

  handleTask = (type, record = null) => {
    this.setState({
      modalVisible: true,
      modalTitle: type === 'add' ? '新增Review' : '编辑Review',
      currentRecord: record,
    });
  };
  // 新增or编辑确认
  handleModalOk = (values) => {
    const { productLineId } = this.props.match.params;
    const isEdit = !!values.id;    
    // 请求数据
    const data = {
      ...values,
      productLineId,
      channel:1,
      companyId:productLineId
    };
    if(isEdit){
      data.isReview=values.status!==4?false:true;
    }
    const url = isEdit ? `/review/update` : `/review/insert`;
    request(url, {
      method: 'POST',
      body: data,
    }).then(res => {
      if (res.code === 200) {
        message.success(`${isEdit ? '编辑' : '新增'}成功`);
        this.setState({ modalVisible: false });
        // 重置筛选条件
        this.setState({
          filterReviewers: undefined,
          filterModifiers: undefined,
          filterStatusValue: undefined,
          filterTitle: '',
          // filterVisible: false
        }, () => {
          this.getReviewList(this.state.current);
        });
      } else {
        message.error(res.msg);
      }
    });
  };
  // 关闭弹窗
  handleModalCancel = () => {
    this.setState({
      modalVisible: false,
      currentRecord: null,
    });
  };

  // 打开编辑弹窗
  handleEdit = (record) => {
    this.handleTask('edit', record);
  };
  // 删除
  handleDelete = (record) => {
    Modal.confirm({
      title: '确定删除该条用例review单吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        request(`/review/delete`, {
          method: 'POST',
          body: {
            id: record.id
          }
        }).then(res => {
          console.log('删除', res); 
          if (res.code === 200) {
            message.success('删除成功');
            this.getReviewList(this.state.current);
          } else {
            message.error(res.msg);
          } 
        });
      }
    });
  };
  // 筛选
  handleFilterSearch = (filters) => {
    this.setState({
      filterReviewers: filters.review,
      filterModifiers: filters.revise,
      filterStatusValue: filters.status,
      filterTitle: filters.title,
      current: 1,
      // filterVisible: false
    }, () => {
      this.getReviewList(1);
    });
  };
//开始Review
  handleStartReview = (record) => {
    this.props.history.push(`/review/caseReview?caseId=${record.caseId}&reviewId=${record.id}`);
  };
  //开始修订
  handleStartRevision = (record) => {
    this.props.history.push(`/review/caseRevision?caseId=${record.caseId}&reviewId=${record.id}`);
  };
  // 查看界面
  handleView = (record) => {
    this.props.history.push(`/reviewContrast?caseId=${record.caseId}&reviewId=${record.id}`);
  };
  getColumns = () => {
    return [
      {
        title: 'ID',
        dataIndex: 'id',
        width: '8%',
      },
      {
        title: '用例名称',
        dataIndex: 'title',
        width: '10%',
        render: (text) => (
          <Tooltip title={text}>
            <span className="table-ellipsis">{text}</span>
          </Tooltip>
        ),
      },
     
      {
        title: 'Review人',
        dataIndex: 'reviewer',
        // width: '10%',
        render: (text) => (
          <Tooltip title={text}>
            <span className="table-ellipsis">{text}</span>
          </Tooltip>
        ),
      },
      {
        title: '修订人',
        dataIndex: 'modifier',
        // width: '10%',
        render: (text) => (
          <Tooltip title={text}>
            <span className="table-ellipsis">{text}</span>
          </Tooltip>
        ),
      },
      {
        title: '创建日期',
        dataIndex: 'gmtCreated',
        width: '12%',
        // render: (text) => moment(text).format('YYYY-MM-DD'),
      },
      {
        title: '截止日期',
        dataIndex: 'cutOffDate',
        width: '12%',
        // render: (text) => moment(text).format('YYYY-MM-DD'),
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: '8%',
        render: (status) => {
          const statusInfo = STATUS_MAP[status];
          return (
            <Tag color={statusInfo.color}>
              {statusInfo.text}
            </Tag>
          );
        },
      },
      {
        title: '备注',
        dataIndex: 'note',
        width: '12%',
        render: (text) => (
          <Tooltip title={text}>
            <span className="table-ellipsis">{text}</span>
          </Tooltip>
        ),
      },
      {
        title: '操作',
        width: '15%',
        render: (_, record) => {
          const { currentUser } = this.state;
          const currentUserId = currentUser?.id;
          const isAdmin = currentUser?.isAdmin;
          // 检查权限
          const hasReviewPermission = record.reviewIds?.some(reviewer => reviewer.id === currentUserId);
          const hasRevisePermission = record.reviseIds?.some(reviser => reviser.id === currentUserId);
          // 按钮配置
          const buttonConfig = {
            review: {
              title: "开始Review",
              icon: "play-circle",
              onClick: () => this.handleStartReview(record),
              show: hasReviewPermission  // 添加显示条件
            },
            revise: {
              title: "开始修订",
              icon: "edit",
              onClick: () => this.handleStartRevision(record),
              show: hasRevisePermission  // 添加显示条件
            },
            view: {
              title: "查看",
              icon: "eye",
              onClick: () => this.handleView(record),
              show: true  // 所有人都可以查看
            },
            edit: {
              title: "修改review信息",
              icon: "form",
              onClick: () => this.handleEdit(record),
              show: isAdmin||currentUserId===record.creator 
            },
            resetReview: {
              title: "重置Review",
              icon: "reload",
              onClick: () => this.handleEdit(record),
              show: isAdmin||currentUserId===record.creator  // 所有人都可以重置Review
            },
            delete: {
              title: "删除",
              icon: "delete",
              onClick: () => this.handleDelete(record),
              show: isAdmin||currentUserId===record.creator  // 所有人都可以删除
            }
          };

          // 不同状态下显示的按钮
          const statusButtons = {
            1: ['review', 'edit', 'delete'],           // Review中
            2: ['revise', 'view', 'edit', 'delete'],   // 修订中
            3: ['view', 'delete'],                     // 完成
            4: ['resetReview', 'delete']  // 过期
          };

          // 获取当前状态对应的按钮，并根据权限过滤
          const currentButtons = (statusButtons[record.status] || [])
            .filter(type => buttonConfig[type].show);
          
          // 生成按钮
          const buttons = currentButtons.map((type, index) => {
            const config = buttonConfig[type];
            return (
              <Tooltip key={type} title={config.title}>
                <a 
                  onClick={config.onClick} 
                  className={`icon-bg${index === 0 ? ' border-a-redius-left' : ''}${index === currentButtons.length - 1 ? ' border-a-redius-right' : ''}`}
                >
                  <Icon type={config.icon} />
                </a>
              </Tooltip>
            );
          });

          return <span>{buttons}</span>;
        },
      },
    ];
  };

  render() {
    const { 
      list, 
      total, 
      current, 
      pageSize, 
      loading, 
      companyName,
      modalVisible,
      modalTitle,
      currentRecord,
      filterVisible,
      filterStatus,
      reviewerOptions,
      modifierOptions
    } = this.state;
    const { match, doneApiPrefix, oeApiPrefix } = this.props;

    return (
      <div className="review-list-container">
        <div className="site-drawer-render-in-current-wrapper">
          <Row className="m-b-10">
            <Col span={18}>
              <div style={{ margin: '10px' }}>
                <span style={{ marginRight: '20px' }}>
                  项目名称：{companyName}
                </span>
                <Tabs defaultActiveKey="all" onChange={this.handleTabChange}>
                  <TabPane tab="全部内容" key="all" />
                  <TabPane tab="与我相关" key="mine" />
                </Tabs>
              </div>
            </Col>
            <Col span={6} className="text-right">
              <Button style={{ marginRight: 16 }} onClick={this.filterHandler}>
                <Icon type="filter" /> 筛选
              </Button>
              <Button type="primary" onClick={() => this.handleTask('add')}>
                <Icon type="plus" /> 新建Review
              </Button>
            </Col>
          </Row>
          <hr style={{ border: '0', backgroundColor: '#e8e8e8', height: '1px' }} />
          
          <Table
            columns={this.getColumns()}
            dataSource={list}
            rowKey="id"
            pagination={{
              current,
              pageSize,
              total,
              onChange: this.handlePageChange,
              onShowSizeChange: this.handlePageChange,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条`,
            }}
            loading={loading}
            scroll={{ y: 'calc(100vh - 270px)' }}
          />
          
         {modalVisible && <ReviewModal
            visible={modalVisible}
            title={modalTitle}
            record={currentRecord}
            onOk={this.handleModalOk}
            onCancel={this.handleModalCancel}
            productLineId={match.params.productLineId}
            doneApiPrefix={doneApiPrefix}
            oeApiPrefix={oeApiPrefix}
          />}
          
          {filterVisible && <ReviewFilterDrawer
            visible={filterVisible}
            onClose={this.closeFilter}
            onSearch={this.handleFilterSearch}
            productLineId={match.params.productLineId}
            filterStatus={filterStatus}
          />}
        </div>
      </div>
    );
  }
}

export default ReviewList; 