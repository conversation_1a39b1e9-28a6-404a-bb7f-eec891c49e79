import React, { useEffect } from 'react';
import { Modal, Form, Input } from 'antd';
// import defaultLogo from '../../../../assets/image/logo.png';

const ProjectFormModal = Form.create()(
  ({ form, visible, editData, onCancel, onOk }) => {
    const { getFieldDecorator, setFieldsValue } = form;

    // 当 editData 变化时，更新表单数据
    useEffect(() => {
      if (visible && editData) {
        // 使用 setFieldsValue 设置表单值
        setFieldsValue({
          name: editData.name,
          code: editData.code,
          logo: editData.logo,
          remark: editData.remark,
          // creator: editData.creator,
          // 其他字段...
        });
      }
    }, [visible, editData]);

    const handleOk = () => {
      form.validateFields((err, values) => {
        if (!err) {
          // 只传递表单值，不在这里重置表单
          onOk(values);
        }
      });
    };

    const handleCancel = () => {
      form.resetFields();
      onCancel();
    };

    return (
      <Modal
        title={editData ? '编辑项目' : '新增项目'}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
        destroyOnClose
        okText="确认"
        cancelText="取消"
      >
        <Form layout="vertical">
          <Form.Item label="项目名称">
            {getFieldDecorator('name', {
              rules: [{ required: true, message: '请输入项目名称' }],
            })(<Input placeholder="请输入项目名称" />)}
          </Form.Item>
          <Form.Item label="项目编码">
            {getFieldDecorator('code', {
              rules: [{ required: true, message: '请输入项目编码' }],
            })(<Input placeholder="请输入项目编码" />)}
          </Form.Item>
          <Form.Item label="厂商图标">
            {getFieldDecorator('logo', {
              rules: [{ required: false }],
            })(<Input placeholder="请输入厂商图标地址" />)}
          </Form.Item>
          <Form.Item label="项目描述">
            {getFieldDecorator('remark', {
              rules: [{ required: true, message: '请输入项目描述' }],
            })(<Input.TextArea placeholder="请输入项目描述" rows={4} />)}
          </Form.Item>
        </Form>
      </Modal>
    );
  },
);

export default ProjectFormModal;
