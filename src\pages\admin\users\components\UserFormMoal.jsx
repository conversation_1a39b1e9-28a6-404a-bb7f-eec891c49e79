import React from 'react';
import { Modal, Form, Input, Select, Radio, message } from 'antd';
import request from '@/utils/axios'; // 导入封装的request
const { Option } = Select;
const FormItem = Form.Item;

class UserFormModal extends React.Component {
  state = {
    projectOptions: [], // 添加项目选项状态
  };

  // 获取项目列表
  fetchProjects = async () => {
    try {
      const response = await request('/company/list', {
        method: 'GET',
      });

      if (response && response.code === 200) {
        // 转换数据格式为Select需要的格式
        const options = response.data.map(project => ({
          label: project.name,
          value: project.id,
        }));
        this.setState({ projectOptions: options });
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error('获取项目列表失败');
    }
  };

  componentDidMount() {
    // 组件挂载时获取项目列表
    this.fetchProjects();
  }

  componentDidUpdate(prevProps) {
    // 当弹窗显示时，设置表单值
    if (this.props.visible && !prevProps.visible) {
      const { form, editData } = this.props;
      if (editData) {
        console.log('editData', editData);
        form.setFieldsValue({
          username: editData.username,
          phone: editData.phone,
          // realName: editData.realName,
          role: editData.authorityName,
          projects: editData.companyId,
          email: editData.email,
          status: editData.isDelete,
        });
      } else {
        form.resetFields();
      }
    }
  }

  handleOk = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.props.onOk(values);
      }
    });
  };

  render() {
    const { visible, onCancel, confirmLoading, editData, form } = this.props;
    const { getFieldDecorator } = form;
    const { projectOptions } = this.state;
    // const projectOptions = [
    //   { label: '项目A', value: '项目A' },
    //   { label: '项目B', value: '项目B' },
    //   { label: '项目C', value: '项目C' },
    // ];
    // 是否离线版本
    const isOffline = process.env.APP_VERSION === 'offline';
    const roleOptions = [
      { label: '管理员', value: 'ROLE_ADMIN' },
      { label: '普通用户', value: 'ROLE_USER' },
    ];

    // const formItemLayout = {
    //   labelCol: { span: 6 },
    //   wrapperCol: { span: 16 },
    // };

    const formItemLayout = {
      labelCol: { span: 8 },
      wrapperCol: { span: 16 },
    };

    // 使用数组来管理所有表单项，方便动态渲染
    const formItems = [
      // 用户名
      <FormItem key="username" label="用户名" {...formItemLayout}>
        {getFieldDecorator('username', {
          rules: [
            { required: true, message: '请输入用户名' },
            // { min: 3, message: '用户名至少3个字符' },
          ],
        })(<Input placeholder="请输入用户名" disabled={!!editData} />)}
      </FormItem>,

      // 密码（仅新增时显示）
      // !editData && (
      //   <FormItem key="password" label="密码" {...formItemLayout}>
      //     {getFieldDecorator('password', {
      //       rules: [
      //         { required: true, message: '请输入密码' },
      //         { min: 6, message: '密码至少6个字符' },
      //       ],
      //     })(<Input.Password placeholder="请输入密码" />)}
      //   </FormItem>
      // ),

      // 姓名
      // <FormItem key="realName" label="姓名" {...formItemLayout}>
      //   {getFieldDecorator('realName', {
      //     rules: [{ required: true, message: '请输入姓名' }],
      //   })(<Input placeholder="请输入姓名" />)}
      // </FormItem>,

      // 系统角色
      <FormItem key="role" label="系统角色" {...formItemLayout}>
        {getFieldDecorator('role', {
          rules: [{ required: true, message: '请选择系统角色' }],
        })(
          <Select placeholder="请选择系统角色" disabled={!isOffline && !!editData}>
            {roleOptions.map(role => (
              <Option key={role.value} value={role.value}>
                {role.label}
              </Option>
            ))}
          </Select>,
        )}
      </FormItem>,
      //   <FormItem key="role" label="系统角色" {...formItemLayout}>
      //   {getFieldDecorator('role', {
      //     rules: [{ required: true, message: '请输入角色名称' }],
      //   })(<Input placeholder="请输入角色名称" />)}
      // </FormItem>,

      // 所属项目
      <FormItem key="projects" label="所属项目" {...formItemLayout}>
        {getFieldDecorator('projects', {
          rules: [{ required: form.getFieldValue('role') === 'ROLE_ADMIN' ? false : true, message: '请选择所属项目' }],
        })(
          <Select
            mode="multiple"
            // placeholder="请选择所属项目,管理员默认拥有全部项目权限"
            placeholder={
              form.getFieldValue('role') === 'ROLE_ADMIN' 
                ? '管理员默认拥有所有项目权限' 
                : '请选择所属项目'
            }
            disabled={form.getFieldValue('role') === 'ROLE_ADMIN'}
            optionFilterProp="label"
          >
            {projectOptions.map(project => (
              <Option
                key={project.value}
                value={project.value}
                label={project.label}
              >
                {project.label}
              </Option>
            ))}
          </Select>,
        )}
      </FormItem>,
      // 手机号
      <FormItem key="phone" label="手机号" {...formItemLayout}>
        {getFieldDecorator('phone', {
          rules: [
            { required: false, message: '请输入手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
          ],
        })(<Input placeholder="请输入手机号" />)}
      </FormItem>,
      // 邮箱
      <FormItem key="email" label="邮箱" {...formItemLayout}>
        {getFieldDecorator('email', {
          rules: [
            { required: false, message: '请输入邮箱' },
            { type: 'email', message: '请输入正确的邮箱格式' },
          ],
        })(<Input placeholder="请输入邮箱" />)}
      </FormItem>,

      // 状态
      <FormItem key="status" label="状态" {...formItemLayout}>
        {getFieldDecorator('status', {
          initialValue: 1,
          rules: [{ required: false, message: '请选择状态' }],
        })(
          <Radio.Group>
            <Radio value={0}>正常</Radio>
            <Radio value={1}>停用</Radio>
          </Radio.Group>,
        )}
      </FormItem>,
    ].filter(Boolean); // 过滤掉不显示的表单项（如编辑时的密码项）

    return (
      <Modal
        title={editData ? '编辑用户' : '新增用户'}
        visible={visible}
        onCancel={onCancel}
        onOk={this.handleOk}
        confirmLoading={confirmLoading}
        width={800}
        okText="确认"
        cancelText="取消"
      >
        <Form layout="horizontal">
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 1fr)',
              gap: '16px', // 将间距统一设置为16px
              alignItems: 'flex-start',
            }}
          >
            {formItems.map((item, index) => (
              <div key={index} style={{ marginBottom: 0 }}>
                {' '}
                {/* 移除额外的底部间距 */}
                {React.cloneElement(item, {
                  style: { marginBottom: 0 }, // 移除Form.Item的默认底部间距
                })}
              </div>
            ))}
          </div>
        </Form>
      </Modal>
    );
  }
}

export default Form.create()(UserFormModal);
