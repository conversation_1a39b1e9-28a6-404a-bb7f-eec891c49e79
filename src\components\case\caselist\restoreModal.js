import React from 'react';
import PropTypes from 'prop-types';
import { Form, message, Modal, TreeSelect, Tag } from 'antd';
import request from '@/utils/axios';
import './index.scss';
import <PERSON><PERSON> from 'js-cookie';
const { TreeNode } = TreeSelect;
const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

class RestoreModal extends React.Component {
  static propTypes = {
    visible: PropTypes.bool,
    formData: PropTypes.object,
    doneApiPrefix: PropTypes.string,
    productLineId: PropTypes.any,
    onSuccess: PropTypes.func,
    onCancel: PropTypes.func,
  };

  constructor(props) {
    super(props);
    this.state = {
      cardTree: [],
      loading: false,
      prevVisible: false, // 添加这个状态来追踪 visible 的变化
    };
  }

  componentDidMount() {
    if (this.props.visible) {
      this.getCardTree();
    }
  }

  componentDidUpdate(prevProps) {
    // 当弹窗从隐藏变为显示时，获取数据
    if (!prevProps.visible && this.props.visible) {
      this.getCardTree();
    }
  }

  getCardTree = () => {
    request(`${this.props.doneApiPrefix}/dir/cardTree`, {
      method: 'GET',
      params: {
        productLineId: this.props.productLineId,
        channel: 1,
        companyId: this.props.productLineId,
      },
    }).then(res => {
      this.setState({ cardTree: res.data ? res.data.children : [] });
    });
  };

  handleOk = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.setState({ loading: true });

        request(`${this.props.doneApiPrefix}/case/recover `, {
          method: 'POST',
          body: {
            ids: this.props.formData.caseIds,
            bizId: values.bizId,
            // companyId: this.props.productLineId,
            modifier:Cookie.get('userName')
          },
        })
          .then(res => {
            if (res.code === 200) {
              message.success('恢复成功');
              this.setState({ loading: false });
              this.props.form.resetFields(); // 重置表单
              this.props.onSuccess();
            } else {
              this.setState({ loading: false });
              message.error(res.msg || '恢复失败');
            }
          })
          .catch(() => {
            this.setState({ loading: false });
            message.error('恢复失败');
          });
    }
    });
  };

  handleCancel = () => {
    this.props.form.resetFields(); // 关闭时重置表单
    this.props.onCancel();
  };

  renderTreeNodes = (data = []) =>
    data.map(item => {
      item.title = <span>{item.text}</span>;
      if (item.children) {
        return (
          <TreeNode
            title={item.title}
            value={item.id}
            key={item.id}
            dataRef={item}
          >
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode {...item} />;
    });

  render() {
    const { visible, formData } = this.props;
    const { getFieldDecorator } = this.props.form;
    const { loading, cardTree } = this.state;
    const { selectedCases = [] } = formData;

    return (
      <Modal
        visible={visible}
        onCancel={this.handleCancel}
        onOk={this.handleOk}
        maskClosable={false}
        wrapClassName="oe-caseModal-style-wrap"
        title="恢复用例"
        okText="确认"
        cancelText="取消"
        width="600px"
        confirmLoading={loading}
      >
        <Form.Item {...formItemLayout} label="用例名称：">
          <div
            style={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '8px',
              minHeight: '32px',
              padding: '4px 0',
            }}
          >
            {selectedCases.map(item => (
              <Tag
                key={item.id}
                color="blue"
                style={{
                  margin: 0,
                  padding: '4px 8px',
                //   backgroundColor: '#f5f5f5',
                  borderRadius: '2px',
                  maxWidth: '100%',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {item.title}
              </Tag>
            ))}
          </div>
        </Form.Item>

        <Form.Item {...formItemLayout} label="用例分类：">
          {getFieldDecorator('bizId', {
            rules: [{ required: true, message: '请选择用例分类' }],
          })(
            <TreeSelect
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              placeholder="请选择用例"
              allowClear
            //   multiple
              treeDefaultExpandAll
            >
              {this.renderTreeNodes(cardTree)}
            </TreeSelect>,
          )}
        </Form.Item>
      </Modal>
    );
  }
}

export default Form.create()(RestoreModal);
