import React, { useState, useEffect, useRef } from 'react';
import { Button, Radio, Table, Icon, message, Spin, Modal } from 'antd';
import request from '@/utils/axios'; // 导入封装的request
import ExportDialog from './ExportDialog';
import LeftPanel from './LeftPanel';
import AgileTCEditor from '../../components/react-mindmap-editor';
import FileUploader from './FileUploader';

import './Home.scss';

const Home = ({ match }) => {
  const [currentPreviewMode, setCurrentPreviewMode] = useState('upload');
  const [selectedFileIndex, setSelectedFileIndex] = useState(-1);
  const [selectedFileId, setSelectedFileId] = useState(null);
  const [selectedFileData, setSelectedFileData] = useState(null);
  const [canShowXmind, setCanShowXmind] = useState(false);
  const [canShowTable, setCanShowTable] = useState(false);
  const [showExportDialogFlag, setShowExportDialogFlag] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [isLeftPanelCollapsed, setIsLeftPanelCollapsed] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [tableData, setTableData] = useState([]); // 单独的表格数据状态
  const [mergeData, setMergeData] = useState({}); // 单元格合并数据
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false);
  const [previewImageList, setPreviewImageList] = useState([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [conversionModel, setConversionModel] = useState(1); // 1:严格模式, 2:兼容模式

  // 创建编辑器引用
  const [editorNode, setEditorNode] = useState(null);
  // 存储待设置的脑图数据
  const [pendingXmindData, setPendingXmindData] = useState(null);
  const leftPanelRef = useRef();
  // 从路由参数获取 productLineId
  const productLineId = match?.params?.productLineId;

  // 监听编辑器节点变化，设置待处理的数据
  useEffect(() => {
    if (editorNode && pendingXmindData) {
      // console.log('设置脑图数据:', pendingXmindData);
      // 使用 setTimeout 确保编辑器完全初始化
      setTimeout(() => {
        editorNode.setEditerData(pendingXmindData);
        setPendingXmindData(null); // 清除待处理数据
      }, 100);
    }
  }, [editorNode, pendingXmindData]);
  // 默认表格数据（可以根据需要调整）
  const defaultTableData = [
    {
      key: '1',
      caseName: '示例用例',
      functionModule: '示例模块',
      testPoint: '示例测试点',
      precondition: '示例前置条件',
      testSteps: '示例测试步骤',
      expectedResult: '示例预期结果',
      actualResult: '通过'
    }
  ];

  // 根据记录id获取预览数据
  const fetchPreviewData = async (recordId, fileInfo, targetMode = null, targetModel = null,changeMode = false) => {
    if (!recordId || !fileInfo) return;

    // 使用传入的目标模式或当前预览模式
    const previewMode = targetMode || currentPreviewMode;
    // 使用传入的目标模型或当前转换模型
    const modelToUse = targetModel !== null ? targetModel : conversionModel;

    // setPreviewLoading(true);
    const params = {
      id: recordId,
      idType: fileInfo.fileType === 'case' ? 2 : 1,  //1:record,2:case
      model : modelToUse

    };
    // if (fileInfo.fileType === 'case') {
    //   params.model = modelToUse;
    // }
    try {
      const response = await request('/docTransfer/getByIdAndType', {
        method: 'GET',
        params
      });

      if (response.code === 200) {
        // 根据数据类型设置预览模式
        setCanShowXmind(true);
        setCanShowTable(true);
        if (!changeMode) {
          setCurrentPreviewMode('xmind');        
        }

        // 解析脑图数据
        const xmindData = JSON.parse(response.data.content);
        // console.log('获取到脑图数据:', xmindData);

        // 获取表格数据并设置到单独的状态中
        const responseTableData = response.data.data || [];
        // console.log('获取到表格数据:', responseTableData);
        setTableData(responseTableData);

        // 获取合并数据
        const responseMergeData = response.data.merge || {};
        // console.log('获取到合并数据:', responseMergeData);
        setMergeData(responseMergeData);

        // 如果编辑器已经初始化，直接设置数据；否则存储待处理
        if (editorNode) {
          // console.log('编辑器已初始化，直接设置数据');
          setTimeout(() => {
            editorNode.setEditerData(xmindData);
          }, 100);
        } else {
          // console.log('编辑器未初始化，存储待处理数据');
          setPendingXmindData(xmindData);
        }

      } else {
        message.error(response.msg || '获取预览数据失败');
      }
    } catch (error) {
      console.error('获取预览数据失败:', error);
      message.error('获取预览数据失败，请检查网络连接');
    } finally {
      // setPreviewLoading(false);
    }
  };

  // 处理文件上传事件
  const handleFileUploaded = (file) => {
    // console.log('文件上传完成:', file.name);
    // 文件上传成功后，切换到records模式并刷新数据
    handleRefreshRecords();
  };

  // 刷新转换记录
  const handleRefreshRecords = () => {
    // 通知左侧面板切换到records模式并刷新数据
    if (leftPanelRef.current) {
      leftPanelRef.current.switchToRecordsAndRefresh();
    }
  };


  // 处理文件删除事件
  const handleDeleteFile = (index, file) => {
    // 处理选中状态
    if (selectedFileIndex === index) {
      setSelectedFileIndex(-1);
      setSelectedFileId(null);
      setSelectedFileData(null);
      // setCurrentPreviewMode('upload');
      setTableData([]); // 清空表格数据
      setMergeData({}); // 清空合并数据
    } else if (selectedFileIndex > index) {
      setSelectedFileIndex(selectedFileIndex - 1);
    }
  };

  // 处理左侧面板文件选择（接收记录id和模式）
  const handleSelectFile = (recordId, index, fileInfo, mode = 'records') => {
    // console.log('fileInfo',fileInfo);
    setSelectedFileIndex(index);
    setSelectedFileId(recordId);
    setSelectedFileData(fileInfo);
    fetchPreviewData(recordId, fileInfo);
  };

  // 处理左侧面板模式切换
  const handleLeftPanelModeChange = (newMode) => {
    // console.log('左侧面板模式切换到:', newMode);
    // 重置右侧区域为默认上传状态
    setCurrentPreviewMode('upload');
    setSelectedFileIndex(-1);
    setSelectedFileId(null);
    setSelectedFileData(null);
    setCanShowXmind(false);
    setCanShowTable(false);
    setPendingXmindData(null); // 清理待处理的脑图数据
    setTableData([]); // 清空表格数据
    setMergeData({}); // 清空合并数据
    setConversionModel(1); // 重置转换模式为严格模式
  };
  // 切换左侧面板的展开/收缩状态
  const toggleLeftPanel = () => {
    setIsLeftPanelCollapsed(!isLeftPanelCollapsed);
  };

  // 获取当前表格数据
  const getCurrentTableData = () => {
    // 如果有表格数据则返回，否则返回默认数据
    return tableData.length > 0 ? tableData : defaultTableData;
  };

  // 获取当前选中的文件数据（用于导出）
  const getCurrentFileData = () => {
    return selectedFileData;
  };

  // 计算单元格合并信息
  const getCellMergeInfo = (columnIndex, rowIndex) => {
    const currentTableData = getCurrentTableData();
    const columnKey = (columnIndex + 1).toString(); // 列索引从1开始
    const mergeInfo = mergeData[columnKey];

    if (!mergeInfo || !Array.isArray(mergeInfo)) {
      return { rowSpan: 1, colSpan: 1, shouldRender: true };
    }

    // 检查当前行是否在合并范围内
    for (let i = 0; i < mergeInfo.length; i++) {
      const [startRow, endRow] = mergeInfo[i];
      const startIndex = startRow - 1; // 转换为0基索引
      const endIndex = endRow - 1;

      if (rowIndex >= startIndex && rowIndex <= endIndex) {
        if (rowIndex === startIndex) {
          // 这是合并区域的第一行，需要渲染并设置rowSpan
          return {
            rowSpan: endIndex - startIndex + 1,
            colSpan: 1,
            shouldRender: true
          };
        } else {
          // 这是合并区域的其他行，不需要渲染
          return {
            rowSpan: 0,
            colSpan: 1,
            shouldRender: false
          };
        }
      }
    }

    // 不在任何合并范围内，正常渲染
    return { rowSpan: 1, colSpan: 1, shouldRender: true };
  };

  // 处理图片预览
  const handleImagePreview = (imageList, index = 0) => {
    setPreviewImageList(imageList);
    setCurrentImageIndex(index);
    setImagePreviewVisible(true);
  };

  // 关闭图片预览
  const handleCloseImagePreview = () => {
    setImagePreviewVisible(false);
    setPreviewImageList([]);
    setCurrentImageIndex(0);
  };

  const handleModeChange = (e) => {
    const newMode = e.target.value;
    setCurrentPreviewMode(newMode);
    // 切换视图模式时重新获取对应模式的预览数据
    // if (selectedFileId && selectedFileData) {
    //   fetchPreviewData(selectedFileId, selectedFileData, newMode);
    // }
  };

  // 处理转换模式变化（严格模式/兼容模式）
  const handleConversionModelChange = (e) => {
    const newModel = e.target.value;
    setConversionModel(newModel);
    // 切换转换模式时重新获取预览数据，直接传入新的模式值
    if (selectedFileId && selectedFileData) {
      fetchPreviewData(selectedFileId, selectedFileData, null, newModel,true);
    }
  };

  const backToUpload = () => {
    setCurrentPreviewMode('upload');
    setSelectedFileIndex(-1);
    setSelectedFileId(null);
    setSelectedFileData(null);
    setCanShowXmind(false);
    setCanShowTable(false);
    setPendingXmindData(null); // 清理待处理的脑图数据
    setTableData([]); // 清空表格数据
    setMergeData({}); // 清空合并数据
    setConversionModel(1); // 重置转换模式为严格模式
  };

  const showExportDialog = () => {
    setShowExportDialogFlag(true);
  };

  // 表格列定义
  const columns = [
    {
      title: '用例名称',
      dataIndex: 'caseName',
      key: 'caseName',
      width: 100,
      align: 'center',
      render: (text, record, index) => {
        const mergeInfo = getCellMergeInfo(0, index); // 第0列
        if (!mergeInfo.shouldRender) {
          return {
            children: text,
            props: {
              rowSpan: 0,
            },
          };
        }
        return {
          children: text,
          props: {
            rowSpan: mergeInfo.rowSpan,
            style: mergeInfo.rowSpan > 1 ? {
              textAlign: 'center',
              verticalAlign: 'middle'
            } : {},
          },
        };
      },
    },
    {
      title: '功能模块',
      dataIndex: 'model',
      key: 'model',
      width: 120,
      align: 'center',
      render: (text, record, index) => {
        const mergeInfo = getCellMergeInfo(1, index); // 第1列
        if (!mergeInfo.shouldRender) {
          return {
            children: text,
            props: {
              rowSpan: 0,
            },
          };
        }
        return {
          children: text,
          props: {
            rowSpan: mergeInfo.rowSpan,
            style: mergeInfo.rowSpan > 1 ? {
              textAlign: 'center',
              verticalAlign: 'middle'
            } : {},
          },
        };
      },
    },
    {
      title: '测试点',
      dataIndex: 'test',
      key: 'test',
      width: 100,
      align: 'center',
      render: (text, record, index) => {
        const mergeInfo = getCellMergeInfo(2, index); // 第2列
        if (!mergeInfo.shouldRender) {
          return {
            children: text,
            props: {
              rowSpan: 0,
            },
          };
        }
        return {
          children: text,
          props: {
            rowSpan: mergeInfo.rowSpan,
            style: mergeInfo.rowSpan > 1 ? {
              textAlign: 'center',
              verticalAlign: 'middle'
            } : {},
          },
        };
      },
    },
    {
      title: '前置条件',
      dataIndex: 'pre',
      key: 'pre',
      width: 120,
      ellipsis: false,
      align: 'center',
      render: (text, record, index) => {
        const mergeInfo = getCellMergeInfo(3, index); // 第3列
        if (!mergeInfo.shouldRender) {
          return {
            children: text,
            props: {
              rowSpan: 0,
            },
          };
        }
        return {
          children: text,
          props: {
            rowSpan: mergeInfo.rowSpan,
            style: mergeInfo.rowSpan > 1 ? {
              textAlign: 'center',
              verticalAlign: 'middle'
            } : {},
          },
        };
      },
    },
    {
      title: '测试步骤',
      dataIndex: 'step',
      key: 'step',
      width: 200,
      ellipsis: true,
      align: 'center',
      render: (text, record, index) => {
        const mergeInfo = getCellMergeInfo(4, index); // 第4列
        const content = (
          <div style={{ whiteSpace: 'pre-line', wordBreak: 'break-word' }}>
            {text}
          </div>
        );

        if (!mergeInfo.shouldRender) {
          return {
            children: content,
            props: {
              rowSpan: 0,
            },
          };
        }
        return {
          children: content,
          props: {
            rowSpan: mergeInfo.rowSpan,
            style: mergeInfo.rowSpan > 1 ? {
              textAlign: 'center',
              verticalAlign: 'middle'
            } : {},
          },
        };
      },
    },
    {
      title: '预期结果',
      dataIndex: 'expectResult',
      key: 'expectResult',
      width: 150,
      ellipsis: false,
      align: 'center',
      render: (text, record, index) => {
        const mergeInfo = getCellMergeInfo(5, index); // 第5列
        if (!mergeInfo.shouldRender) {
          return {
            children: text,
            props: {
              rowSpan: 0,
            },
          };
        }
        return {
          children: text,
          props: {
            rowSpan: mergeInfo.rowSpan,
            style: mergeInfo.rowSpan > 1 ? {
              textAlign: 'center',
              verticalAlign: 'middle'
            } : {},
          },
        };
      },
    },
    {
      title: '实际测试结果',
      dataIndex: 'actualResult',
      key: 'actualResult',
      width: 120,
      align: 'center',
      render: (text, record, index) => {
        const mergeInfo = getCellMergeInfo(6, index); // 第6列
        if (!mergeInfo.shouldRender) {
          return {
            children: text,
            props: {
              rowSpan: 0,
            },
          };
        }
        return {
          children: text,
          props: {
            rowSpan: mergeInfo.rowSpan,
            style: mergeInfo.rowSpan > 1 ? {
              textAlign: 'center',
              verticalAlign: 'middle'
            } : {},
          },
        };
      },
    },
    {
      title: '备注',
      dataIndex: 'note',
      key: 'note',
      width: 120,
      align: 'center',
      render: (note, record, index) => {
        const mergeInfo = getCellMergeInfo(7, index); // 第7列

        let content = '';
        if (note && Array.isArray(note) && note.length > 0) {
          content = (
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px', justifyContent: 'center' }}>
              {note.slice(0, 3).map((imageUrl, imgIndex) => (
                <div
                  key={imgIndex}
                  style={{
                    width: '30px',
                    height: '30px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    overflow: 'hidden',
                    cursor: 'pointer',
                    position: 'relative'
                  }}
                  onClick={() => handleImagePreview(note, imgIndex)}
                >
                  <img
                    src={imageUrl}
                    alt={`图片${imgIndex + 1}`}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                  <div
                    style={{
                      display: 'none',
                      width: '100%',
                      height: '100%',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: '#f5f5f5',
                      fontSize: '12px',
                      color: '#999',
                      position: 'absolute',
                      top: 0,
                      left: 0
                    }}
                  >
                    <Icon type="picture" />
                  </div>
                </div>
              ))}
              {note.length > 3 && (
                <div
                  style={{
                    width: '30px',
                    height: '30px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f5f5f5',
                    cursor: 'pointer',
                    fontSize: '12px',
                    color: '#666'
                  }}
                  onClick={() => handleImagePreview(note, 3)}
                >
                  +{note.length - 3}
                </div>
              )}
            </div>
          );
        }

        if (!mergeInfo.shouldRender) {
          return {
            children: content,
            props: {
              rowSpan: 0,
            },
          };
        }
        return {
          children: content,
          props: {
            rowSpan: mergeInfo.rowSpan,
            style: mergeInfo.rowSpan > 1 ? {
              textAlign: 'center',
              verticalAlign: 'middle'
            } : {},
          },
        };
      },
    },
  ];

  return (
    <div className="converter-home">
      {/* 双栏布局容器 */}
      <div className={`main-container ${isLeftPanelCollapsed ? 'left-panel-collapsed' : ''}`}>
        {/* 左侧转换记录区域 */}
        <LeftPanel
          ref={leftPanelRef}
          selectedIndex={selectedFileIndex}
          onSelectFile={handleSelectFile}
          onDeleteFile={handleDeleteFile}
          isCollapsed={isLeftPanelCollapsed}
          onTogglePanel={toggleLeftPanel}
          productLineId={productLineId}
          onModeChange={handleLeftPanelModeChange}
        />

        {/* 右侧内容区域 */}
        <div className="right-panel">
          <div className="content-container">
            {/* 默认上传状态 */}
            {currentPreviewMode === 'upload' && (
              <div className="upload-section">
                <FileUploader
                  onFileUploaded={handleFileUploaded}
                  onRefreshRecords={handleRefreshRecords}
                  productLineId={productLineId}
                />
              </div>
            )}

            {/* 预览模式 */}
            {currentPreviewMode !== 'upload' && (
              <div className="preview-section">
                {/* 预览工具栏 */}
                <div className="preview-toolbar">
                  <div className="toolbar-left">
                    <Button className="back-button" onClick={backToUpload}>
                      <Icon type="arrow-left" />
                      返回上传
                    </Button>
                    {selectedFileData && (
                      <div className="current-file-info">
                        <span className="file-name">
                          {selectedFileData.name}
                        </span>
                        <span className="file-type">
                          {selectedFileData.conversion}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="toolbar-right">
                    {/* 转换模式选择器 - 只在用例选择时显示 */}
                    {selectedFileData && (
                      <div className="conversion-model-selector">
                        <span className="selector-label">转换模式：</span>
                        <Radio.Group
                          value={conversionModel}
                          onChange={handleConversionModelChange}
                          className="conversion-model-radio-group"
                        >
                          <Radio value={1}>严格模式</Radio>
                          <Radio value={2}>兼容模式</Radio>
                        </Radio.Group>
                      </div>
                    )}
                    <div className="mode-tabs">
                      <Radio.Group
                        value={currentPreviewMode}
                        onChange={handleModeChange}
                        className="mode-radio-group"
                      >
                        {canShowXmind && (
                          <Radio.Button value="xmind" className="mode-radio-button">
                            <Icon type="bar-chart" style={{ marginRight: '5px' }} />
                            <span>思维导图</span>
                          </Radio.Button>
                        )}
                        {canShowTable && (
                          <Radio.Button value="table" className="mode-radio-button">
                            <Icon type="table" style={{ marginRight: '5px' }} />
                            <span>表格视图</span>
                          </Radio.Button>
                        )}
                      </Radio.Group>
                    </div>
                    <Button
                      type="primary"
                      className="export-button"
                      onClick={showExportDialog}
                    >
                      <Icon type="download" />
                      导出文件
                    </Button>
                  </div>
                </div>

                {/* 预览内容区域 */}
                <div className="preview-content">
                  {previewLoading ? (
                    <div className="loading-container">
                      <Spin size="large" />
                      <div>加载预览数据中...</div>
                    </div>
                  ) : (
                    <>
                      {/* XMind 预览模式 - 始终渲染，通过CSS控制显示/隐藏 */}
                      <div className={`xmind-preview ${currentPreviewMode === 'xmind' ? 'active' : 'hidden'}`}>
                        <AgileTCEditor
                          ref={node => setEditorNode(node)}
                          readOnly={true}
                          mediaShow={true}
                          editorStyle={{ height: '100%' }}
                          type="compare"
                        />
                      </div>

                      {/* 表格预览模式 - 始终渲染，通过CSS控制显示/隐藏 */}
                      <div className={`table-preview ${currentPreviewMode === 'table' ? 'active' : 'hidden'}`}>
                        <Table
                          dataSource={getCurrentTableData()}
                          columns={columns}
                          className="custom-table"
                          scroll={{ y: 'calc(100vh - 150px)' }}
                          size="middle"
                          bordered
                          rowKey="id"
                          pagination={false}
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 导出弹窗 */}
      <ExportDialog
        visible={showExportDialogFlag}
        onClose={() => setShowExportDialogFlag(false)}
        fileData={getCurrentFileData()}
        fileId={selectedFileId}
        conversionModel={conversionModel}
      />

      {/* 图片预览弹窗 */}
      <Modal
        visible={imagePreviewVisible}
        footer={null}
        onCancel={handleCloseImagePreview}
        width="80%"
        style={{ top: 20 }}
        bodyStyle={{ padding: '20px', textAlign: 'center' }}
        title={`图片预览 (${currentImageIndex + 1}/${previewImageList.length})`}
      >
        {previewImageList.length > 0 && (
          <div>
            <div style={{ marginBottom: '20px', minHeight: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <img
                src={previewImageList[currentImageIndex]}
                alt={`图片${currentImageIndex + 1}`}
                style={{
                  maxWidth: '100%',
                  maxHeight: '400px',
                  objectFit: 'contain',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px'
                }}
                onError={(e) => {
                  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFYzNkgyMFYyMFoiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTI4IDI4SDM2VjM2SDI4VjI4WiIgZmlsbD0iI0Q5RDlEOSIvPgo8L3N2Zz4K';
                  e.target.style.width = '200px';
                  e.target.style.height = '200px';
                }}
              />
            </div>

            {previewImageList.length > 1 && (
              <div style={{ display: 'flex', justifyContent: 'center', gap: '10px' }}>
                <Button
                  disabled={currentImageIndex === 0}
                  onClick={() => setCurrentImageIndex(currentImageIndex - 1)}
                >
                  <Icon type="left" /> 上一张
                </Button>
                <span style={{ lineHeight: '32px', margin: '0 10px' }}>
                  {currentImageIndex + 1} / {previewImageList.length}
                </span>
                <Button
                  disabled={currentImageIndex === previewImageList.length - 1}
                  onClick={() => setCurrentImageIndex(currentImageIndex + 1)}
                >
                  下一张 <Icon type="right" />
                </Button>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Home; 




