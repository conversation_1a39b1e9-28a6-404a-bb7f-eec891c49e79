import React from 'react';
import 'antd/dist/antd.css';
import { Layout, Icon, Menu, Dropdown, message,Modal } from 'antd';
import getQueryString from '@/utils/getCookies';
import '../pages/landing/less/index.less';
import './header.scss';
import request from '@/utils/axios';
import { router } from 'umi';
const { Header } = Layout;
const { SubMenu } = Menu;
import Cookies from 'js-cookie';
const getCookies = getQueryString.getCookie;

class Headers extends React.Component {
  // 项目id
  state = {
    productLineId: '1', // 默认产品线ID，实际应该从接口获取或其他方式
    currentProject: null, // 当前项目
  };
  componentDidMount() {
    // if (!getCookies('username')) {
    //   // window.location.href = `/login?jumpto=${window.location.href}`;
    // }
    if (!Cookies.get('jwt')) {
      Modal.confirm({
        title: 'JWT 缺失',
        content: '登录信息已失效，请刷新页面重新登录',
        okText: '确认刷新',
        cancelText: '取消',
        onOk: () => {
          window.location.reload();
        }
      });
    }
    
  }
  // 获取用户角色信息
  getUserRole = () => {
    const userInfo = sessionStorage.getItem('userInfo');
    if (userInfo) {
      try {
        const parsedInfo = JSON.parse(userInfo);
        // console.log('parsedInfo',parsedInfo)
        return parsedInfo.isAdmin; // 返回是否是管理员
      } catch (error) {
        return false;
      }
    }
    return false;
  };

  // 获取当前选中的菜单key
  // getSelectedMenuKey = () => {
  //   const pathname = window.location.pathname;
  //   console.log(pathname);
  //   if (pathname.includes('/case/caseList')) {
  //     return '/case/caseList';
  //   }
  //   if (pathname === '/') {
  //     return '/';
  //   }
  //   if (pathname.includes('/admin/users')) {
  //     return '/admin/users';
  //   }
  //   if (pathname.includes('/admin/project')) {
  //     return '/admin/project';
  //   }
  //    // 如果是在 admin 下的任何页面，设置 SubMenu 展开
  //    if (pathname.startsWith('/admin')) {
  //     return 'admin';
  //   }
  //   return '';
  // };
  // 获取当前选中的菜单key
  getSelectedMenuKey = () => {
    // 获取 hash 路径，去掉开头的 #
    const hashPath = window.location.hash.slice(1);    
    // 精确匹配首页
    if (!hashPath || hashPath === '/') {
      return '/';
    }
    
    // 使用 startsWith 来匹配子路径
    if (hashPath.startsWith('/case/caseList')) {
      return '/case/caseList';
    }
    if (hashPath.startsWith('/admin/users')) {
      return '/admin/users';
    }
    if (hashPath.startsWith('/admin/project')) {
      return '/admin/project';
    }
    
    // 如果是在 admin 下的任何页面，设置 SubMenu 展开
    if (hashPath.startsWith('/admin')) {
      return 'admin';
    }
    
    return '';
  };
  // 登出
  handleDropdownClick = () => {
    // request(`/user/quit`, {
    //   method: 'POST',
    // }).then(res => {
    //   if (res && res.code === 200) {
    //     Cookies.remove('token')
    //     Cookies.remove('username')
    //     // Cookies.remove('userInfo')
    //     sessionStorage.removeItem('userInfo')
    //     window.location.href = `/login?jumpto=${window.location.href}`;
    //   } else {
    //     message.error(res.msg);
    //   }
    // });
    // Cookies.remove('token');
    // Cookies.remove('username');
    // // Cookies.remove('userInfo')
    // sessionStorage.removeItem('userInfo');
    // sessionStorage.removeItem('currentProject');
    // window.location.href = `/login?jumpto=${window.location.href}`;
  };

  // 添加导航处理函数
  handleNavigation = (path) => {
    router.push(path);
  };

  render() {
    const { productLineId, currentProject } = this.state;
    const selectedKey = this.getSelectedMenuKey();
    const isAdmin = this.getUserRole();
    const menu = (
      <Menu className="menu" onClick={this.handleDropdownClick}>
        <Menu.Item key="logout">
          <span>
            <Icon type="logout" />
            退出登录
          </span>
        </Menu.Item>
      </Menu>
    );

    // 新增导航菜单
    const navigationMenu = (
      <Menu
        mode="horizontal"
        theme="dark"
        // defaultSelectedKeys={[window.location.pathname]}
        selectedKeys={[selectedKey]}
        style={{
          lineHeight: '64px',
          display: 'inline-block',
          marginLeft: '50px',
        }}
      >
        <Menu.Item key="/">
          <a href="/">
            <Icon type="home" />
            首页
          </a>
        </Menu.Item>
        {/* <Menu.Item key="/case/caseList">
          <a href={`/case/caseList/${productLineId}`}>
            <Icon type="profile" />
            用例管理
          </a>
        </Menu.Item> */}

        {/* 只有管理员才能看到后台管理菜单 */}
        {isAdmin && (
          <SubMenu
            key="admin"
            title={
              <span>
                <Icon type="setting" />
                后台管理
              </span>
            }
          >
            <Menu.Item key="/admin/users" onClick={() => this.handleNavigation('/admin/users')}>
              <Icon type="team" />
              用户管理
            </Menu.Item>
            <Menu.Item key="/admin/project" onClick={() => this.handleNavigation('/admin/project')}>
              <Icon type="project" />
              项目管理
            </Menu.Item>
          </SubMenu>
        )}
      </Menu>
    );

    return (
      <Header style={{ zIndex: 9 }}>
        <div style={{ float: 'left', display: 'flex', alignItems: 'center' }}>
          <a href="/" style={{ color: '#fff', fontSize: 24, marginRight: 40,marginLeft:30 }}>
            {/* AgileTC  */}
            EcoCase
          </a>
          {navigationMenu}
          {/* 显示当前项目名称 */}
          {/* {currentProject && (
            <span
              style={{
                color: '#fff',
                marginLeft: '20px',
                fontSize: '14px',
                opacity: 0.85,
              }}
            >
              当前项目: {currentProject.name}
            </span>
          )} */}
        </div>
        {getCookies('userName') ? (
          // <Dropdown
          //   overlay={menu}
          //   overlayClassName="dropStyle"
          //   placement="bottomLeft"
          // >
          //   <div className="user">
          //     <Icon type="user" className="userIcon" />
          //     <span className="username">{getCookies('userName')}</span>
          //     <Icon type="down" className="dowm" />
          //   </div>
          // </Dropdown>
          <div className="user">
          <Icon type="user" className="userIcon" />
          <span className="username">{getCookies('userName')}</span>
          {/* <Icon type="down" className="dowm" />  */}
        </div>
        ) : null}
      </Header>
    ) ;
  }
}
export default Headers;
