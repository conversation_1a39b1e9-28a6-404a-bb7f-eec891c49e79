# 用例管理页面视图切换功能说明

## 功能概述

在用例管理页面添加了视图切换功能，支持在"默认视图"和"需求视图"之间切换，为用户提供不同的数据展示方式。

## 功能特性

### 1. 视图状态控制
- 通过 `/company/get` 接口返回的 `enableStatusView` 字段控制是否显示视图切换功能
- 只有当该字段为 `true` 时，才会在界面上显示视图切换按钮

### 2. 视图切换按钮
- 位置：表格顶部快速筛选区域右侧
- 样式：单选按钮组，包含"默认视图"和"需求视图"两个选项
- 默认选中"默认视图"

### 3. 默认视图
保持原有的表格展示方式，包含以下列：
- 用例ID
- 用例名称
- 关联需求
- 最近更新人
- 创建人
- 创建时间
- Review情况
- 操作（发起Review、编辑用例、创建测试任务、复制用例、删除/导出等）

### 4. 需求视图
新增的视图模式，包含以下列：
- 需求单：显示关联的需求ID，带有特殊样式
- 用例更新时间：显示最后修改时间
- 测试用例：显示用例名称，可点击跳转
- 负责QA：显示负责的QA人员
- 用例状态：显示用例当前状态，包括：
  - 未上传（灰色）
  - 待Review（蓝色）
  - Review中（橙色）
  - 已Review（绿色）
  - 已修订（紫色）
- 操作：包含发起review、上传用例、忽略、删除、导出等按钮

## 技术实现

### 1. 状态管理
在 `CaseLists` 组件中添加了以下状态：
- `viewMode`: 当前视图模式（'default' | 'requirement'）
- `enableStatusView`: 是否启用状态视图功能

### 2. 数据获取
- 默认视图：使用原有的 `/case/list` 接口
- 需求视图：预留了接口调用逻辑，当前使用Mock数据

### 3. 样式设计
- 视图切换按钮：采用Ant Design的Radio.Button组件
- 需求视图表格：添加了专门的CSS类名和样式
- 状态显示：不同状态使用不同颜色区分

## 使用说明

### 1. 启用功能
确保 `/company/get` 接口返回 `enableStatusView: true`

### 2. 切换视图
点击表格顶部的"默认视图"或"需求视图"按钮进行切换

### 3. 需求视图操作
- **发起Review**：点击审核图标发起用例Review流程
- **上传用例**：点击上传图标上传用例文件（功能待实现）
- **忽略**：点击忽略图标忽略该用例（功能待实现）
- **删除/导出**：与默认视图保持一致

## 后续开发

### 1. 接口对接
需要将需求视图的Mock数据替换为真实的后端接口：
```javascript
// 在 getCaseList 方法中替换
const url = `${this.props.doneApiPrefix}/case/requirementView`;
```

### 2. 功能完善
- 实现"上传用例"功能
- 实现"忽略用例"功能
- 添加更多筛选和排序选项

### 3. 性能优化
- 添加数据缓存机制
- 优化大数据量下的渲染性能

## 文件修改清单

1. `src/components/case/caselist/index.js` - 主组件，添加视图切换逻辑
2. `src/components/case/caselist/list.js` - 列表组件，添加需求视图列配置
3. `src/components/case/caselist/mockData.js` - 添加需求视图Mock数据
4. `src/components/case/caselist/index.scss` - 添加视图切换相关样式

## 测试建议

1. 验证视图切换按钮的显示/隐藏逻辑
2. 测试两种视图模式下的数据展示
3. 验证需求视图中各操作按钮的功能
4. 测试样式在不同屏幕尺寸下的表现
5. 验证数据加载和错误处理逻辑
