import React, { Component } from 'react';
import { Icon } from 'antd';

const common = {
  width: '14',
  height: '14',
  version: '1.1',
  xmlns: 'http://www.w3.org/2000/svg',
};
const color = {
  disabled: '#e8e8e8',
  default: '#595959',
};

const addChild = (disabled, style) => (
  <svg viewBox="0 0 1365 1024" {...common} {...style}>
    <path
      d="M682.666667 771.413333V682.666667a68.266667 68.266667 0 0 1 68.266666-68.266667h546.133334a68.266667 68.266667 0 0 1 68.266666 68.266667v273.066666a68.266667 68.266667 0 0 1-68.266666 68.266667h-546.133334a68.266667 68.266667 0 0 1-68.266666-68.266667v-88.746666H361.813333V436.906667H409.6V341.333333h136.533333v95.573334H457.386667v334.506666H682.666667zM136.533333 95.573333H0V0h136.533333v95.573333z m409.6 0H409.6V0h136.533333v95.573333z m-204.8 0H204.8V0h136.533333v95.573333z m0 341.333334H204.8V341.333333h136.533333v95.573334z m-204.8 0H0V341.333333h136.533333v95.573334zM0 276.548267v-123.562667h95.573333v123.562667H0z m649.762133 0v-123.562667H750.933333v123.562667H649.762133zM750.933333 95.573333H614.4V0h136.533333v95.573333z m0 341.333334H614.4V341.333333h136.533333v95.573334z m27.306667 273.066666v218.453334h491.52v-218.453334h-491.52z"
      fill={disabled ? color['disabled'] : color['default']}
    ></path>
  </svg>
);
const addSibling = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path
      d="M409.736533 614.4l0.546134-177.493333H301.602133V341.333333h136.533334v68.266667h67.7888l-1.092267 204.8h319.214933a68.266667 68.266667 0 0 1 68.266667 68.266667v273.066666a68.266667 68.266667 0 0 1-68.266667 68.266667H136.533333a68.266667 68.266667 0 0 1-68.266666-68.266667v-273.066666a68.266667 68.266667 0 0 1 68.266666-68.266667h273.2032zM233.335467 95.573333H68.266667V0h165.0688v95.573333z m409.6 0h-136.533334V0h136.533334v95.573333z m0 341.333334h-136.533334V341.333333h136.533334v95.573334z m-204.8-341.333334h-136.533334V0h136.533334v95.573333z m-204.8 341.333334H72.4992V341.333333h160.836267v95.573334zM71.202133 276.548267v-123.562667h121.173334v123.562667H71.202133z m697.1392 0v-123.562667h105.130667v123.562667h-105.130667zM874.018133 95.573333H711.202133V0h162.816v95.573333z m-0.682666 341.333334H711.2704V341.333333h162.2016v95.573334zM163.84 928.426667h632.900267v-218.453334H163.84v218.453334z"
      fill={disabled ? color['disabled'] : color['default']}
    ></path>
  </svg>
);
const addParent = (disabled, style) => (
  <svg viewBox="0 0 1280 1024" {...common} {...style}>
    <path
      d="M364.8 384v339.2H576v-3.776h89.6v115.84H576V812.8H275.2V384H64a64 64 0 0 1-64-64V64a64 64 0 0 1 64-64h512a64 64 0 0 1 64 64v256a64 64 0 0 1-64 64H364.8z m339.2 281.6H576V576h128v89.6z m384 0h-128V576h128v89.6z m0 320h-128V896h128v89.6z m-192-320h-128V576h128v89.6z m0 320h-128V896h128v89.6z m-192 0H576V896h128v89.6z m481.152-150.336v-115.84H1280v115.84h-94.848zM1280 665.6h-128V576h128v89.6z m0 320h-128V896h128v89.6zM89.6 89.6v204.8h460.8V89.6H89.6z"
      fill={disabled ? color['disabled'] : color['default']}
    ></path>
  </svg>
);
const block = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path
      d="M512 1024A511.99872 511.99872 0 1 0 468.650775 1.794556 512.169386 512.169386 0 0 0 0.00128 512.00128a511.99872 511.99872 0 0 0 511.99872 511.99872z"
      fill="#FFFFFF"
      fillOpacity={disabled ? 0.4 : 1}
    ></path>
    <path
      d="M512 938.66688A426.6656 426.6656 0 1 0 511.914667 85.250347 426.6656 426.6656 0 0 0 512 938.66688z"
      fill="#d81e06"
      fillOpacity={disabled ? 0.4 : 1}
    ></path>
    <path
      d="M512 938.66688a426.6656 426.6656 0 0 0 426.6656-426.6656H85.3344a426.6656 426.6656 0 0 0 426.6656 426.6656z"
      fill="#FFED83"
      fillOpacity={disabled ? 0.4 : 1}
      data-spm-anchor-id="a313x.7781069.0.i5"
    ></path>
  </svg>
);

const fail = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path
      d="M0 512A512 512 0 1 0 512 0 512 512 0 0 0 0 512z"
      fill="#FFED83"
      fillOpacity={disabled ? 0.4 : 1}
      data-spm-anchor-id="a313x.7781069.0.i41"
    ></path>
    <path
      d="M782.826255 253.254397a37.559846 37.559846 0 0 1 0 51.727156l-465.48949 465.599314a36.571429 36.571429 0 0 1-51.727155-51.727156l465.489489-465.599314a37.559846 37.559846 0 0 1 51.727156 0z"
      fill="#d81e06"
      fillOpacity={disabled ? 0.4 : 1}
      data-spm-anchor-id="a313x.7781069.0.i44"
    ></path>
    <path
      d="M265.554698 253.254397a37.559846 37.559846 0 0 1 51.727155 0l465.48949 465.48949a36.571429 36.571429 0 0 1-51.727156 51.727155L265.554698 305.091377a37.559846 37.559846 0 0 1 0-51.83698z"
      fill="#d81e06"
      fillOpacity={disabled ? 0.4 : 1}
      data-spm-anchor-id="a313x.7781069.0.i42"
    ></path>
  </svg>
);

const checked = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path
      d="M509.750303 514.249697m-509.750303 0a509.750303 509.750303 0 1 0 1019.500606 0 509.750303 509.750303 0 1 0-1019.500606 0Z"
      fill="#6AC259"
      fillOpacity={disabled ? 0.4 : 1}
    ></path>
    <path
      d="M250.957576 537.05697a19.859394 19.859394 0 0 1 0-28.780606l28.780606-28.780606a19.859394 19.859394 0 0 1 28.780606 0l2.01697 2.094545 113.105454 121.250909a9.929697 9.929697 0 0 0 14.351515 0L713.69697 317.129697h2.094545a19.859394 19.859394 0 0 1 28.780606 0l28.780606 28.780606a19.859394 19.859394 0 0 1 0 28.780606l-328.921212 341.333333a19.859394 19.859394 0 0 1-28.780606 0L254.991515 543.030303z"
      fill="#FFFFFF"
      fillOpacity={disabled ? 0.4 : 1}
    ></path>
  </svg>
);

const skip = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path
      d="M747.3152 415.6416a256.0512 256.0512 0 0 0-489.472 96.768H341.504a170.6496 170.6496 0 0 1 327.6288-58.624l-115.0976 20.9408 227.84 116.736 48.2816-251.392-82.8416 75.5712zM0 512C0 229.2224 229.1712 0 512 0c282.7776 0 512 229.1712 512 512 0 282.7776-229.1712 512-512 512-282.7776 0-512-229.1712-512-512z"
      fill="#BE96F9"
      fillOpacity={disabled ? 0.4 : 1}
      p-id="577"
    ></path>
  </svg>
  // <svg viewBox="0 0 1024 1024" {...common} {...style}>
  //   <path
  //     d="M511.936 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z"
  //     fill="#9BABC9"
  //     fillOpacity={disabled ? 0.4 : 1}
  //   ></path>
  //   <path
  //     d="M303.808 505.216h-24.832a23.04 23.04 0 0 0 0 46.08h24.832a23.04 23.04 0 0 0 0-46.08z m110.336 0h-24.832a23.04 23.04 0 0 0 0 46.08h24.832a22.912 22.912 0 0 0 23.04-23.04 23.296 23.296 0 0 0-23.04-23.04z m110.336 0h-24.832a23.04 23.04 0 0 0 0 46.08h25.088a22.912 22.912 0 0 0 23.04-23.04 23.296 23.296 0 0 0-23.04-23.04z m236.8 6.4L640.448 391.04a22.784 22.784 0 0 0-32.512-0.512 22.784 22.784 0 0 0-0.512 32.512l0.512 0.512 81.408 81.664H609.728a23.04 23.04 0 0 0 0 46.08h79.616L607.936 632.96a23.04 23.04 0 0 0 32 33.024l0.512-0.512 120.576-120.832a23.104 23.104 0 0 0 0.512-32.768z"
  //     fill="#FFFFFF"
  //     fillOpacity={disabled ? 0.4 : 1}
  //   ></path>
  // </svg>
);

const clear = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path
      d="M674.225 111.4c-7.9-7.8-20.6-7.8-28.5 0L5.925 751.2c-7.9 7.9-7.9 20.7 0 28.5l124.4 124.4c9.5 8.5 21.7 13.6 34.4 14.3h398.7c11.1 0 26.6-6.4 34.4-14.3l420.3-420.3c7.9-7.9 7.9-20.7 0-28.5L674.225 111.4zM558.325 828.3c-9.5 8.6-21.7 13.6-34.5 14.3H215.225c-12.8-0.7-24.9-5.7-34.4-14.3l-58.6-58.7c-7.9-7.9-7.9-20.7 0-28.5l234.5-234.6c7.9-7.9 20.7-7.9 28.5 0l233.1 233.2c7.8 7.9 7.8 20.6 0 28.5l-60 60.1z"
      fillOpacity={disabled ? 0.4 : 1}
    ></path>
  </svg>
);

const stylePaste = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path
      d="M411.880727 905.169455v-279.272728h149.876364v279.272728z m54.272-279.645091v-124.881455h412.904728V325.771636h-64.605091v116.363637h-698.181819v-279.272728h698.181819v116.363637h111.104v268.055273h-412.904728v78.289454z"
      fillOpacity={disabled ? 0.4 : 1}
    ></path>
  </svg>
);

const selectedAll = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path
      d="M1024 0v109.714286h-109.714286V0H1024z m-226.742857 0v109.714286H687.542857V0h109.714286zM570.514286 0v109.714286H460.8V0h109.714286zM336.457143 0v109.714286H226.742857V0h109.714286zM109.714286 0v109.714286H0V0h109.714286zM1024 226.742857v109.714286h-109.714286V226.742857H1024z m-914.285714 0v109.714286H0V226.742857h109.714286zM1024 453.485714v109.714286h-109.714286V453.485714H1024z m-914.285714 0v109.714286H0V453.485714h109.714286z m241.371428-109.714285L702.171429 438.857143 599.771429 541.257143l102.4 102.4-51.2 58.514286-102.4-102.4L438.857143 702.171429 351.085714 343.771429zM1024 687.542857v109.714286h-109.714286V687.542857H1024z m-914.285714 0v109.714286H0V687.542857h109.714286z m226.742857 226.742857V1024H226.742857v-109.714286h109.714286z m234.057143 0V1024H460.8v-109.714286h109.714286z m226.742857 0V1024H687.542857v-109.714286h109.714286z m226.742857 0V1024h-109.714286v-109.714286H1024z m-914.285714 0V1024H0v-109.714286h109.714286z"
      fillOpacity={disabled ? 0.4 : 1}
    ></path>
  </svg>
);
// 放大
const zoomIn = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path
      d="M943.8 892.5l-201.4-201c24.2-29 43.9-61.3 58.7-96.3 20-47.3 30.1-97.6 30.1-149.4s-10.1-102-30.1-149.4c-19.3-45.7-47-86.7-82.1-122-35.2-35.2-76.2-62.9-121.9-82.2-47.3-20-97.5-30.2-149.3-30.2-51.8 0-102 10.2-149.3 30.2-45.7 19.3-86.7 47-121.9 82.2s-62.8 76.3-82.1 122c-20 47.3-30.1 97.6-30.1 149.4s10.1 102 30.1 149.4c19.3 45.7 47 86.7 82.1 122 35.2 35.2 76.2 62.9 121.9 82.2 47.3 20 97.5 30.2 149.3 30.2 51.7 0 102-10.2 149.3-30.2 34.6-14.7 66.6-34.1 95.3-58l201.5 201c6.9 6.9 15.9 10.3 24.9 10.3 9.1 0 18.1-3.5 25-10.4 13.8-13.7 13.8-36.1 0-49.8zM669.7 666.6c-0.4 0.4-0.8 0.7-1.2 1.1-0.3 0.3-0.6 0.6-0.8 0.9-59 58.3-137 90.4-219.9 90.4-83.5 0-162.1-32.6-221.2-91.7-59.1-59.1-91.6-137.8-91.6-221.4s32.5-162.3 91.6-221.4c59.1-59.1 137.6-91.7 221.2-91.7s162.1 32.6 221.2 91.7c59.1 59.1 91.6 137.8 91.6 221.4 0 83.3-32.3 161.6-90.9 220.7z"
      // fill={disabled ? color['disabled'] : color['default']}
    ></path>
    <path
      d="M573.7 419H473v-98c0-19.5-13-35.3-32.5-35.3S408 301.5 408 321v98H305.3c-19.5 0-35.3 13-35.3 32.5s15.8 32.5 35.3 32.5H408v105.4c0 19.5 13 35.3 32.5 35.3s32.5-15.8 32.5-35.3V484h100.7c19.5 0 35.3-13 35.3-32.5S593.2 419 573.7 419z"
      // fill={disabled ? color['disabled'] : color['default']}
    ></path>
  </svg>
);
// 缩小
const zoomOut = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path
      d="M946.9 897.7L744 695.2c24.4-29.2 44.2-61.7 59.1-97 20.2-47.7 30.4-98.3 30.4-150.5s-10.2-102.8-30.4-150.5c-19.5-46-47.3-87.4-82.8-122.9s-76.8-63.4-122.8-82.8c-47.7-20.2-98.3-30.4-150.4-30.4S344.4 71.3 296.8 91.5c-46 19.5-87.3 47.4-122.8 82.8-35.5 35.5-63.3 76.8-82.8 122.9-20.2 47.7-30.4 98.3-30.4 150.5S71 550.5 91.2 598.2c19.5 46 47.3 87.4 82.8 122.9s76.8 63.4 122.8 82.8c47.7 20.2 98.3 30.4 150.4 30.4s102.7-10.2 150.4-30.4c34.9-14.8 67.1-34.4 96.1-58.5l203 202.6c6.9 6.9 16 10.4 25.1 10.4 9.1 0 18.2-3.5 25.2-10.5 13.8-13.8 13.8-36.3-0.1-50.2zM447.2 763.2c-84.2 0-163.3-32.8-222.8-92.4C164.8 611.2 132 532 132 447.7c0-84.3 32.8-163.5 92.3-223.1 59.5-59.6 138.7-92.4 222.8-92.4s163.3 32.8 222.8 92.4c59.5 59.6 92.3 138.8 92.3 223.1 0 83.9-32.5 162.8-91.6 222.3-0.4 0.4-0.8 0.8-1.2 1.1-0.3 0.3-0.6 0.6-0.8 0.9-59.3 58.9-137.9 91.2-221.4 91.2z"
      // fill={disabled ? color['disabled'] : color['default']}
    ></path>
    <path
      d="M574 416H303.7c-19.7 0-35.6 12.8-35.6 32.5S284 481 303.7 481H574c19.7 0 35.6-12.8 35.6-32.5S593.7 416 574 416z"
      // fill={disabled ? color['disabled'] : color['default']}
    ></path>
  </svg>
);
const target = (disabled, style) => (
  <svg viewBox="0 0 1024 1024" {...common} {...style}>
    <path d="M1024.002828 483.15493h-122.892019a390.850704 390.850704 0 0 0-360.803756-359.181221V0.060094h-60.093896v124.093897A390.910798 390.910798 0 0 0 122.894847 483.15493H0.002828v60.093896h122.892019a390.910798 390.910798 0 0 0 357.258216 359.000939V1024h60.093896v-121.569953A390.79061 390.79061 0 0 0 901.050715 543.248826h122.892019V483.15493z m-483.695775 359.18122V723.530516h-60.093896v118.625353A330.997183 330.997183 0 0 1 182.988743 543.248826H300.472312V483.15493H182.988743a330.997183 330.997183 0 0 1 297.224414-298.907043v116.28169h60.093896V184.067606A330.937089 330.937089 0 0 1 841.016912 483.15493H723.533344v60.093896h117.483568a330.937089 330.937089 0 0 1-300.709859 299.087324z"></path>
  </svg>
);

export default class CustomIcon extends Component {
  render() {
    const { type, disabled, style } = this.props;
    const btnMap = {
      addChild: () => {
        return addChild(disabled, style);
      },
      addSibling: () => {
        return addSibling(disabled, style);
      },
      addParent: () => {
        return addParent(disabled, style);
      },
      block: () => {
        return block(disabled, style);
      },
      fail: () => {
        return fail(disabled, style);
      },
      checked: () => {
        return checked(disabled, style);
      },
      skip: () => {
        return skip(disabled, style);
      },
      clear: () => {
        return clear(disabled, style);
      },
      stylePaste: () => {
        return stylePaste(disabled, style);
      },
      selectedAll: () => {
        return selectedAll(disabled, style);
      },
      zoomIn: () => {
        return zoomIn(disabled, style);
      },
      zoomOut: () => {
        return zoomOut(disabled, style);
      },
      target: () => {
        return target(disabled, style);
      },
    };
    return <Icon component={btnMap[type]} {...this.props} />;
  }
}
