import React from 'react';
import {
  Button,
  Icon,
  message,
  Dropdown,
  Menu,
  Empty,
  Spin,
  Modal,
} from 'antd';
import styles from './index.less';
import AgileTCEditor from '../../components/react-mindmap-editor';
import request from '@/utils/axios';
import AnnotationItem from '../../components/AnnotationItem';
import Cookie from 'js-cookie';
import { removeCommentCount } from '@/utils/common';
import getWsUrl from '@/utils/wsUrl';
import router from 'umi/router';
class ReviewPage extends React.Component {
  constructor(props) {
    super(props);
    this.statusMap = {
      全部: '全部',
      '1': '未处理',
      '2': '同意',
      '3': '忽略',
    };
    this.state = {
      isAnnotationVisible: true,
      loading: false,
      info: null,
      annotations: [],
      selectedAnnotationId: null,
      filterStatus: '全部',
      filterStatusName: '全部', // 添加用于显示的状态名称
      filterUser: '所有人',
      filterUserName: '所有人', // 添加用于显示的用户名称
      userList: [], // 用户列表
      caseId: '',
      reviewId: '',
      annotationsLoading: false, // 添加批注加载状态
      reviewInfo: null,
      productLineId: undefined,
    };
    this.editorRef = React.createRef();
  }

  componentDidMount() {
    const { caseId, reviewId } = this.props.location.query;
    this.setState({ caseId, reviewId });
    this.getReviewInfo();
    this.fetchAnnotations();
    // this.fetchUserList();
  }
  handleNodeSelect = nodeId => {
    // 当节点被选中时,更新 selectedAnnotationId
    this.setState({ selectedAnnotationId: nodeId });

    // 找到对应的批注
    const annotation = this.state.annotations.find(
      item => item.nodeId === nodeId,
    );
    if (annotation) {
      // 如果找到对应的批注,滚动到视图中
      const element = document.getElementById(`annotation-${nodeId}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }
  };
  fetchAnnotations = async () => {
    this.setState({ annotationsLoading: true }); // 开始加载时设置状态
    const { caseId, reviewId } = this.props.location.query;
    const { filterStatus, filterUser } = this.state;

    const params = {
      pageNum: 1,
      pageSize: 99999,
      reviewId: reviewId,
    };

    // 只有当不是默认值时才添加筛选参数
    if (filterStatus !== '全部') {
      params.status = filterStatus;
    }
    if (filterUser !== '所有人') {
      params.creator = filterUser;
    }

    request(`/comment/queryByPage`, {
      method: 'GET',
      params: params,
    })
      .then(res => {
        if (res && res.code === 200) {
          const arr = res.data.dataSources
            .map(item => {
              return {
                id: item.nodeId,
                nodeId: item.nodeId,
                nodeTitle: item.nodeName,
                nodeStatus: item.nodeStatus,
                comments:
                  item.comments?.map(comment => {
                    return {
                      id: comment.id,
                      username: comment.creatorName,
                      content: comment.comment,
                      time: comment.gmtCreated,
                      status: comment.status,
                      statusName: comment.statusName,
                      images:
                        comment.commentFile
                          ?.filter(file => {
                            // 判断文件是否为图片(通过扩展名判断)
                            const fileName = file.fileName.toLowerCase();
                            return (
                              fileName.endsWith('.png') ||
                              fileName.endsWith('.jpg') ||
                              fileName.endsWith('.jpeg') ||
                              fileName.endsWith('.gif') ||
                              fileName.endsWith('.bmp') ||
                              fileName.endsWith('.webp')
                            );
                          })
                          .map(file => {
                            return {
                              id: file.id,
                              url: file.filePath,
                              name: file.fileName,
                            };
                          }) || [],
                      files:
                        comment.commentFile
                          ?.filter(file => {
                            // 判断文件是否不是图片
                            const fileName = file.fileName.toLowerCase();
                            return !(
                              fileName.endsWith('.png') ||
                              fileName.endsWith('.jpg') ||
                              fileName.endsWith('.jpeg') ||
                              fileName.endsWith('.gif') ||
                              fileName.endsWith('.bmp') ||
                              fileName.endsWith('.webp')
                            );
                          })
                          .map(file => {
                            return {
                              id: file.id,
                              url: file.filePath,
                              name: file.fileName,
                            };
                          }) || [],
                      replies:
                        comment.commentDiscuss?.map(reply => {
                          return {
                            id: reply.id,
                            username: reply.creatorName,
                            content: reply.discuss,
                            time: reply.gmtCreated,
                            // status: reply.status,
                          };
                        }) || [],
                    };
                  }) || [],
              };
            })
            .sort((a, b) => {
              // 将nodeStatus=1的排到后面
              if (a.nodeStatus === 1 && b.nodeStatus !== 1) {
                return 1;
              }
              if (a.nodeStatus !== 1 && b.nodeStatus === 1) {
                return -1;
              }
              return 0;
            });
          this.setState({
            annotations: JSON.parse(JSON.stringify(arr)),
            annotationsLoading: false, // 加载完成后设置状态
          });
        } else {
          message.error(res ? res.msg || '获取批注失败' : '网络错误，无法获取批注');
          this.setState({ annotationsLoading: false }); // 加载失败也要设置状态
        }
      })
      .catch((error) => {
        console.error('获取批注失败:', error);
        message.error('获取批注失败，请检查网络连接');
        this.setState({ annotationsLoading: false }); // 加载失败也要设置状态
      });
  };
  // 获取用户列表
  fetchUserList = async () => {
    request(`/user/companyUserList`, {
      method: 'GET',
      params: {
        id: this.state.productLineId,
      },
    })
      .then(res => {
        if (res && res.code === 200) {
          const userList = (res.data || []).map(user => ({
            name: user.username,
            id: user.id,
          }));

          this.setState({
            userList,
          });
        } else {
          console.error('获取用户列表失败:', res);
          message.error('获取用户列表失败: ' + (res ? res.msg || '未知错误' : '网络错误'));
        }
      })
      .catch(err => {
        console.error('获取用户列表出错:', err);
        message.error('获取用户列表失败，请检查网络连接');
        this.setState({ loading: false });
      });
  };

  handleAnnotationEdit = async (annotationId, commentId, content) => {
    try {
      const params = {
        id: commentId,
        comment: content,
      };

      request(`/comment/update`, {
        method: 'POST',
        body: params,
      })
        .then(res => {
          if (res && res.code === 200) {
            // this.setState({ annotations: updatedAnnotations });
            message.success('评论更新成功');
            // this.fetchAnnotations();
            this.editorNode.sendReviewOperationChange();
          } else {
            message.error(res ? res.msg || '评论更新失败' : '网络错误，评论更新失败');
          }
        })
        .catch((error) => {
          console.error('评论更新错误:', error);
          message.error('评论更新失败，请检查网络连接');
        });
    } catch (error) {
      console.error('评论更新错误:', error);
      message.error('评论更新失败');
    }
  };

  handleAnnotationDelete = async (annotationId, commentId) => {
    try {
      request(`/comment/delete`, {
        method: 'POST',
        body: {
          id: commentId,
        },
      })
        .then(res => {
          if (res && res.code === 200) {
            message.success('删除成功');
            this.editorNode.minder.execCommand('comment', -1);
            this.editorNode.sendReviewOperationChange();
            // this.fetchAnnotations(); // 重新获取批注列表
          } else {
            message.error(res ? res.msg || '删除失败' : '网络错误，删除失败');
          }
        })
        .catch((error) => {
          console.error('删除批注错误:', error);
          message.error('删除失败，请检查网络连接');
        });
    } catch (error) {
      console.error('删除批注错误:', error);
      message.error('删除失败');
    }
  };

  handleAnnotationClick = id => {
    this.setState({ selectedAnnotationId: id });
    // // 这里需要调用脑图组件的方法来选中对应节点
    const annotation = this.state.annotations.find(item => item.nodeId === id);
    if (annotation && this.editorNode) {
      // 调用脑图组件的选中节点方法
      this.editorNode.selectNodeById(annotation.nodeId);
    }
  };

  toggleAnnotation = () => {
    this.setState(prevState => ({
      isAnnotationVisible: !prevState.isAnnotationVisible,
    }));
  };

  handleFilterChange = (type, value) => {
    if (type === 'filterUser') {
      if (value === '所有人') {
        this.setState(
          {
            filterUser: '所有人',
            filterUserName: '所有人',
          },
          () => {
            this.fetchAnnotations();
          },
        );
      } else {
        const user = this.state.userList.find(u => u.id === value);
        this.setState(
          {
            filterUser: value, // 保存ID
            filterUserName: user ? user.name : value, // 保存名字
          },
          () => {
            this.fetchAnnotations();
          },
        );
      }
    } else if (type === 'filterStatus') {
      // 处理状态筛选
      const statusName = this.statusMap[value] || value;
      this.setState(
        {
          filterStatus: value,
          filterStatusName: statusName,
        },
        () => {
          this.fetchAnnotations();
        },
      );
    } else {
      this.setState({ [type]: value }, () => {
        this.fetchAnnotations();
      });
    }
  };

  getFilteredAnnotations = () => {
    // 直接返回annotations，因为筛选已经在接口层完成
    return this.state.annotations;
  };

  getReviewInfo = () => {
    const { reviewId } = this.props.location.query;
    request(`/review/getReviewById`, {
      method: 'GET',
      params: { id: reviewId },
    }).then(res => {
      if (res && res.code === 200) {     
        this.setState({ reviewInfo: res.data });
        // setTimeout(() => {
          // this.editorNode.initOnEvent(this.editorNode.minder);
          // this.editorNode.initHotbox(this.editorNode.minder);
          // this.editorNode.minder.refresh();
          // this.editorNode.minder.fire('contentchange');
        // }, 500);
        if(this.state.reviewInfo.status !== 2){
          const { caseId, reviewId } = this.props.location.query;
          let msg = ''
          let url = `/review/reviewList/${this.state.reviewInfo.companyId}`
          if(this.state.reviewInfo.status === 1){
            msg = '当前状态为review中,无法进行内容修订';
            // url = `/reviewContrast?caseId=${caseId}&reviewId=${reviewId}`;
          }else if(this.state.reviewInfo.status === 3){
            msg = '当前状态为已完成,无法进行内容修订,请前往结果页面查看修订内容!';
            url = `/reviewContrast?caseId=${caseId}&reviewId=${reviewId}`;
          }else if(this.state.reviewInfo.status === 4){
            msg = '当前状态已过期,无法进行内容修订!';
            // url = `/reviewContrast?caseId=${caseId}&reviewId=${reviewId}`;
          }


          Modal.confirm({
            title: '提示',
            content: msg,
            okText: '确定',
            cancelButtonProps: { style: { display: 'none' } },
            // cancelText: '取消',
            onOk: () => {
              // this.getReviewInfo();
              router.push(url);
            },
            // onCancel: () => {
            //   this.getReviewInfo();
            // },
          });
        }
        this.setState({ productLineId: this.state.reviewInfo.productLineId });
        if (this.state.reviewInfo.productLineId) {
          this.fetchUserList();
        }
      } else {
        message.error(res ? res.msg || '获取信息失败' : '网络错误，无法获取信息');
      }
    }).catch(error => {
      console.error('获取信息失败:', error);
      message.error('获取信息失败，请检查网络连接');
    });
  };
  // 完成review
  handleReview = () => {
    // console.log('完成修订',this.editorNode?.minder?.exportJson());

    const { reviewId } = this.props.location.query;
    request(`/review/updateStatus`, {
      method: 'POST',
      body: {
        id: reviewId,
        type: 2, // 1:完成review 2:完成修订
      },
    }).then(async res => {
      if (res && res.code === 200) {
        await this.updateCase();
        // this.getReviewInfo();
        message.success('操作成功');
      } else {
        message.error(res ? res.msg || '操作失败' : '网络错误，操作失败');
      }
    }).catch(err => {
      console.error('完成修订出错:', err);
      message.error('操作失败，请检查网络连接');
    });
  };
  // 保存review
  updateCase = async (flag = false) => {
    const { caseId, reviewId } = this.props.location.query;
    const minderData = this.editorNode?.minder?.exportJson();
    const processedData = removeCommentCount(minderData);
    const param = {
      id: caseId,
      title: '更新内容，实际不会保存title',
      modifier: Cookie.get('userName'),
      caseContent: JSON.stringify(processedData),
      reviewType: 2,
      reviewId: reviewId,
    };

    let url = `/case/update`;
    try {
      const res = await request(url, { method: 'POST', body: param });
      if (res && res.code === 200) {
        // message.success('保存内容成功');
        this.editorNode?.ws?.sendMessage('review_complete_event', {
          reviewType: 2,
        }); // 给服务器发送review完成消息
      } else {
        message.error(res ? res.msg || '保存内容失败' : '网络错误，保存内容失败');
      }
    } catch (error) {
      console.error('保存内容出错:', error);
      message.error('保存内容失败，请检查网络连接');
    }
  };
  handleReviewComplete = message => {
    const { caseId, reviewId } = this.props.location.query;
    Modal.confirm({
      title: '提示',
      content: '当前内容已完成修订,内容已锁定,请前往结果页面查看修订内容',
      okText: '确定',
      cancelButtonProps: { style: { display: 'none' } },
      // cancelText: '取消',
      onOk: () => {
        // this.getReviewInfo();
        router.push(`/reviewContrast?caseId=${caseId}&reviewId=${reviewId}`);
      },
      // onCancel: () => {
      //   this.getReviewInfo();
      // },
    });
  };
  // 回复批注内容
  handleAnnotationReply = (commentId, replyId, content) => {
    const params = {
      // commentId: commentId,
      commentId: replyId,
      discuss: content,
    };
    request(`/commentDiscuss/insert`, {
      method: 'POST',
      body: params,
    })
      .then(res => {
        if (res && res.code === 200) {
          // this.setState({ annotations: updatedAnnotations });
          message.success('评论成功');
          // this.fetchAnnotations();
          this.editorNode.sendReviewOperationChange();
        } else {
          message.error(res ? res.msg || '评论失败' : '网络错误，评论失败');
        }
      })
      .catch((error) => {
        console.error('评论错误:', error);
        message.error('评论失败，请检查网络连接');
      });
  };
  // 删除回复内容
  handleReplyDelete = replyId => {
    try {
      request(`/commentDiscuss/delete`, {
        method: 'POST',
        body: {
          id: replyId,
        },
      })
        .then(res => {
          if (res && res.code === 200) {
            message.success('删除成功');
            // this.fetchAnnotations(); // 重新获取批注列表
            this.editorNode.sendReviewOperationChange();
          } else {
            message.error(res ? res.msg || '删除失败' : '网络错误，删除失败');
          }
        })
        .catch((error) => {
          console.error('删除回复错误:', error);
          message.error('删除失败，请检查网络连接');
        });
    } catch (error) {
      console.error('删除回复错误:', error);
      message.error('删除失败');
    }
  };
  // 编辑回复内容
  handleReplyEdit = (replyId, commentId, content) => {
    const params = {
      id: replyId,
      discuss: content,
    };
    request(`/commentDiscuss/update`, {
      method: 'POST',
      body: params,
    })
      .then(res => {
        if (res && res.code === 200) {
          message.success('编辑成功');
          // this.fetchAnnotations();
          this.editorNode.sendReviewOperationChange();
        } else {
          message.error(res ? res.msg || '编辑失败' : '网络错误，编辑失败');
        }
      })
      .catch((error) => {
        console.error('编辑回复错误:', error);
        message.error('编辑失败，请检查网络连接');
      });
  };
  // 删除图片
  handleDeleteImage = async imageId => {
    try {
      const response = await request('/commentFile/delete', {
        method: 'POST',
        body: {
          id: imageId,
        },
      });

      if (response && response.code === 200) {
        message.success('图片删除成功');
        this.editorNode.sendReviewOperationChange();
        // this.fetchAnnotations(); // 刷新评论列表
      } else {
        message.error(response ? response.msg || '删除失败' : '网络错误，删除失败');
      }
    } catch (error) {
      console.error('删除图片错误:', error);
      message.error('删除图片失败，请重试');
    }
  };
  // 删除附件
  handleDeleteFile = async fileId => {
    try {
      const response = await request('/commentFile/delete', {
        method: 'POST',
        body: {
          id: fileId,
        },
      });

      if (response && response.code === 200) {
        message.success('附件删除成功');
        this.editorNode.sendReviewOperationChange();
        // this.fetchAnnotations(); // 刷新评论列表
      } else {
        message.error(response ? response.msg || '删除失败' : '网络错误，删除失败');
      }
    } catch (error) {
      console.error('删除附件错误:', error);
      message.error('删除附件失败，请重试');
    }
  };
  // 同意或忽略批注通知服务端
  handleAnnotationStatus = () => {
    this.editorNode.sendReviewOperationChange();
  };
  render() {
    const {
      isAnnotationVisible,
      selectedAnnotationId,
      filterStatus,
      filterStatusName,
      filterUser,
      filterUserName,
      userList,
      reviewInfo,
      caseId,
      reviewId,
      annotationsLoading,
      annotations,
    } = this.state;
    const filteredAnnotations = this.getFilteredAnnotations();
    // console.log('reviewInfo',reviewInfo);
    // let readOnly = false;
    // if(reviewInfo?.status === 2){
    //   readOnly = true;
    // }else{
    //   readOnly = false;
    // }
    return (
      <div className={styles.revise_container}>
        <div className={styles.header}>
          <div className={styles.left}>
            <Icon
              type="left"
              className={styles.backIcon}
              onClick={() => {
                if(reviewInfo?.companyId){
                  router.push(`/review/reviewList/${reviewInfo.companyId}`);
                }else{
                  router.push(`/`);
                }
              }}
            />
            <div className={styles.titleWrapper}>
              <span className={styles.title}>
                {reviewInfo?.status === 1 && (
                  <span style={{ color: '#1890ff', marginRight: '8px' }}>
                    [Review中]
                  </span>
                )}
                {reviewInfo?.status === 2 && (
                  <span style={{ color: '#faad14', marginRight: '8px' }}>
                    [修订中]
                  </span>
                )}
                {reviewInfo?.status === 3 && (
                  <span style={{ color: '#52c41a', marginRight: '8px' }}>
                    [已完成]
                  </span>
                )}
                {reviewInfo?.status === 4 && (
                  <span style={{ color: '#f5222d', marginRight: '8px' }}>
                    [已过期]
                  </span>
                )}
                {reviewInfo?.title}(id:{reviewInfo?.id})
              </span>
              <span className={styles.updateTime}>
                最近修改：{reviewInfo?.gmtUpdated}
              </span>
            </div>
          </div>
          <div className={styles.right}>
            {/* <span>剩余26天12小时17分53秒</span> */}
            {/* <span>1个review者</span> */}
            <span>
              截止时间:
              {reviewInfo?.cutOffDate ? reviewInfo?.cutOffDate : '无截止时间'}
            </span>
            {reviewInfo?.status === 2 && (
              <Button
                type="primary"
                onClick={() => {
                  this.editorNode.onButtonSave();
                }}
              >
                保存
              </Button>
            )}
            {reviewInfo?.status === 2 && (
              <Button type="primary" onClick={this.handleReview}>
                完成修订
              </Button>
            )}
            {reviewInfo?.status === 3 && (
              <Button type="primary" disabled>
                已完成修订
              </Button>
            )}
          </div>
        </div>

        <div className={styles.revise_content}>
          <div className={styles.mindmap}>
            <AgileTCEditor
              ref={editorNode => (this.editorNode = editorNode)}
              tags={['前置条件', '执行步骤', '预期结果']}
              editorStyle={{ height: '100%' }}
              uploadUrl="/thirdapp/ecocase/api/file/uploadAttachment"
              wsUrl={getWsUrl()}
              // wsParam = {{ transports:['websocket','xhr-polling','jsonp-polling'], query: { caseId: caseId, recordId: itemid, user: user }}}
              wsParam={{
                transports: ['websocket', 'xhr-polling', 'jsonp-polling'],
                path: '/thirdapp/ecocase/ws/api/socket.io',
                query: {
                  caseId: caseId,
                  recordId: undefined,
                  reviewId: reviewId,
                  user: Cookie.get('userName') || '未知',
                },
              }}
              readOnly={false}
              type="revise"
              toolbar={{
                image: true,
                theme: ['classic-compact', 'fresh-blue', 'fresh-green-compat'],
                template: ['default', 'right', 'fish-bone'],
                noteTemplate: '# test',
                addFactor: false,
              }}
              mediaShow={false}
              progressShow={false}
              onNodeSelect={this.handleNodeSelect}
              onReviewChange={this.fetchAnnotations}
              onReviewComplete={this.handleReviewComplete}
            />
          </div>
          <div className={styles.annotationContainer}>
            <div className={styles.collapseBtn} onClick={this.toggleAnnotation}>
              <Icon type={isAnnotationVisible ? 'right' : 'left'} />
            </div>
            <div
              className={`${styles.annotation} ${
                !isAnnotationVisible ? styles.collapsed : ''
              }`}
            >
              <div className={styles.annotationHeader}>
                <div className={styles.title}>批注列表</div>
                <div className={styles.filter}>
                  <Dropdown
                    overlay={
                      <Menu
                        selectedKeys={[
                          filterUser === '所有人'
                            ? '所有人'
                            : userList.find(u => u.id === filterUser)?.name ||
                              filterUser,
                        ]}
                        onClick={({ key }) =>
                          this.handleFilterChange(
                            'filterUser',
                            key === '所有人'
                              ? '所有人'
                              : userList.find(u => u.name === key)?.id || key,
                          )
                        }
                      >
                        <Menu.Item key="所有人">所有人</Menu.Item>
                        {userList.map(user => (
                          <Menu.Item key={user.name}>{user.name}</Menu.Item>
                        ))}
                      </Menu>
                    }
                    trigger={['click']}
                  >
                    <span className={styles.filterDropdown}>
                      {filterUserName} <Icon type="down" />
                    </span>
                  </Dropdown>
                  <Dropdown
                    overlay={
                      <Menu
                        selectedKeys={[filterStatus]}
                        onClick={({ key }) =>
                          this.handleFilterChange('filterStatus', key)
                        }
                      >
                        <Menu.Item key="全部">全部</Menu.Item>
                        <Menu.Item key="1">未处理</Menu.Item>
                        <Menu.Item key="2">同意</Menu.Item>
                        <Menu.Item key="3">忽略</Menu.Item>
                      </Menu>
                    }
                    trigger={['click']}
                  >
                    <span className={styles.filterDropdown}>
                      {filterStatusName} <Icon type="down" />
                    </span>
                  </Dropdown>
                </div>
              </div>
              <div className={styles.annotationList}>
                <Spin spinning={annotationsLoading}>
                  {annotations.length > 0 ? (
                    annotations.map(annotation => (
                      <AnnotationItem
                        key={annotation.id}
                        data={annotation}
                        onEdit={this.handleAnnotationEdit}
                        onDelete={this.handleAnnotationDelete}
                        onReply={this.handleAnnotationReply}
                        onReplyEdit={this.handleReplyEdit}
                        onReplyDelete={this.handleReplyDelete}
                        isSelected={selectedAnnotationId === annotation.nodeId}
                        onClick={this.handleAnnotationClick}
                        pageType="revise"
                        minder={this.editorNode.minder}
                        fetchAnnotations={this.fetchAnnotations}
                        reviewStatus={reviewInfo?.status}
                        onAnnotationStatus={this.handleAnnotationStatus}
                        onDeleteImage={this.handleDeleteImage}
                        onDeleteFile={this.handleDeleteFile}
                      />
                    ))
                  ) : (
                    <div className={styles.emptyState}>
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description="暂无批注"
                      />
                    </div>
                  )}
                </Spin>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default ReviewPage;
