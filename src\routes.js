const { NODE_ENV, APP_VERSION } = process.env; // 添加 APP_VERSION 环境变量
// 根据版本区分路由配置
const getRoutes = () => {
  const commonRoutes = [
    {
      exact: true,
      path: '/caseManager/:productLineId/:caseId/:itemid/:iscore',
      component: './testTask/index.js',
      title:'用例详情'
    },
    {
      exact: true,
      path: '/caseManager/historyContrast/:caseId1/:caseId2',
      component: './contrast/seeResult.js',
      title:'版本比较'
    },
    {
      path: '/review/caseReview',
      component: './review/index.jsx',
      title: '用例review',
    },
    {
      path: '/review/caseRevision',
      component: './revise/index.jsx',
      title: '用例修订',
    },
    {
      path: '/reviewContrast',
      component: './reviewContrast/index.jsx',
      title: 'review查看',
    },
  ];

  const offlineRoutes = [
    {
      path: '/login',
      component: './landing/login.jsx',
      title: '登录'
    },
    // {
    //   path: '/register',
    //   component: './landing/register.jsx',
    //   title: '注册'
    // }
  ];

  const mainRoutes =   {
    path: '/',
    component: '../layouts/BasicLayout/index.js',
    routes: [
      // {
      //   exact: true,
      //   path: '/',
      //   component: './landing/index.jsx',
      // },
      {
        exact: true,
        path: '/',
        component: './landing/newIndex.jsx',
        title: '首页'
      }, 
      {
        path: '/admin',
        routes: [
          {
            path: '/admin/users',
            component: './admin/users/index.jsx',
            exact: true,
            title: '用户管理',
          },
          {
            path: '/admin/project',
            component: './admin/project/index.jsx',
            exact: true,
            title: '项目管理',
          },
        ],
      },
      {
        path: '/case/caseList/:productLineId',
        component: './casepage/index.js',
        title:'用例管理'
      },
      {
        path: '/review/reviewList/:productLineId',
        component: './review/ReviewList.jsx',
        title:'review管理'
      },
      // {
      //   path: '/caseManager/:productLineId/:caseId/:itemid/:iscore',
      //   component: './testTask/index.js',
      //   title:'用例详情'
      // },
      // {
      //   path: '/login',
      //   component: './landing/login.jsx',
      // },
      {
        path: '/history/:caseId',
        component: './contrast/index.jsx',
      },
      // {
      //   path: '/caseManager/historyContrast/:caseId1/:caseId2',
      //   component: './contrast/seeResult.js',
      // },
      {
        path: '/convert/:productLineId',
        component: './convert/Home.jsx',
      },

      {
        path: '*',
        redirect: '/',
      },
    ],
    
  }

  return [
    ...commonRoutes,
    ...(APP_VERSION === 'offline' ? offlineRoutes : []),
    mainRoutes
  ];
};
module.exports = getRoutes();