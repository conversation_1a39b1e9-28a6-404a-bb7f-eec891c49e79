import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, Form, message, Icon, Upload } from 'antd';
import request from '@/utils/axios';

const { TextArea } = Input;

const CommentDrawer = ({ visible, onClose, onSubmit, selectedNode }) => {
  const [comment, setComment] = useState('');
  const [files, setFiles] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  // 清空所有内容的函数
  const resetForm = () => {
    setComment('');
    setFiles([]);
  };

  // 监听visible变化，当弹窗打开或关闭时清空内容
  useEffect(() => {
    resetForm();
  }, [visible]);

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey && e.keyCode === 13 && visible) { // Ctrl + Enter
        handleSubmit();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [visible, comment]);

  // 关闭弹窗的处理函数，确保清空内容
  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handlePaste = async (e) => {
    const clipboardData = e.clipboardData;
    const items = clipboardData.items;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      
      if (item.type.indexOf('image') > -1) {
        e.preventDefault();
        const file = item.getAsFile();
        // 检查文件大小
        if (file.size > 10 * 1024 * 1024) {
          message.error('文件大小不能超过10MB');
          return;
        }
        
        const formData = new FormData();
        formData.append('file', file);

        try {
          const res = await request('/file/uploadAttachment', {
            method: 'POST',
            body: formData,
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });

          if (res.data && res.data[0].url) {
            setFiles(prev => [...prev, {
              name: res.data[0].originalFileName,
              url: res.data[0].url,
              id: Date.now(),
              type: 'image'
            }]);
          } else {
            message.error('图片上传失败');
          }
        } catch (error) {
          console.error('上传图片失败:', error);
          message.error('图片上传失败');
        }
      }
    }
  };

  const handleRemoveFile = (fileId) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const handlePreview = (imageUrl) => {
    setPreviewImage(imageUrl);
    setPreviewVisible(true);
  };

  const handleSubmit = () => {
    if (!comment.trim()) {
      message.warning('请输入批注内容!');
      return;
    }
    
    if (!selectedNode) {
      message.warning('请先选择节点');
      return;
    }
    
    onSubmit({
      comment,
      commentFiles: files.map(file => ({
        fileName: file.name,
        filePath: file.url,
        fileType: file.type
      }))
    });
    
    setComment('');
    setFiles([]);
  };

  const getSelectedNodesText = () => {
    if (!selectedNode) return '';
    if (Array.isArray(selectedNode)) {
      return `已选择 ${selectedNode.length} 个节点`;
    }
    return `已选择节点: ${selectedNode.getText()}`;
  };

  const customUploadRequest = async ({ file, onSuccess, onError }) => {
    // 检查文件大小
    if (file.size > 10 * 1024 * 1024) {
      message.error('文件大小不能超过10MB');
      onError(new Error('文件大小不能超过10MB'));
      return;
    }
    
    const formData = new FormData();
    formData.append('file', file);

    try {
      const res = await request('/file/uploadAttachment', {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (res.data && res.data[0].url) {
        const isImage = file.type.indexOf('image') > -1;
        setFiles(prev => [...prev, {
          name: file.name,
          url: res.data[0].url,
          id: Date.now(),
          type: isImage ? 'image' : 'file',
          fileType: file.type
        }]);
        onSuccess();
      } else {
        message.error('附件上传失败');
        onError();
      }
    } catch (error) {
      console.error('上传附件失败:', error);
      message.error('附件上传失败');
      onError();
    }
  };

  // 获取文件图标
  const getFileIcon = (fileType) => {
    if (fileType.includes('pdf')) return 'file-pdf';
    if (fileType.includes('word') || fileType.includes('doc')) return 'file-word';
    if (fileType.includes('excel') || fileType.includes('sheet') || fileType.includes('csv')) return 'file-excel';
    if (fileType.includes('ppt') || fileType.includes('presentation')) return 'file-ppt';
    if (fileType.includes('zip') || fileType.includes('rar') || fileType.includes('tar') || fileType.includes('gz')) return 'file-zip';
    if (fileType.includes('text') || fileType.includes('txt')) return 'file-text';
    return 'file';
  };

  const renderFileList = () => {
    if (files.length === 0) return null;

    const imageFiles = files.filter(file => file.type === 'image');
    const otherFiles = files.filter(file => file.type !== 'image');

    return (
      <>
        {imageFiles.length > 0 && (
          <Form.Item label="已上传图片" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
              {imageFiles.map(image => (
                <div 
                  key={image.id}
                  style={{ 
                    position: 'relative',
                    width: '100px',
                    height: '100px',
                    cursor: 'pointer',
                  }}
                  className="image-container"
                >
                  <img 
                    src={image.url} 
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      borderRadius: '4px'
                    }}
                    onClick={() => handlePreview(image.url)}
                    alt=""
                  />
                  <div 
                    style={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      width: '24px',
                      height: '24px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: 'rgba(255, 255, 255, 0.8)',
                      borderRadius: '0 4px 0 4px',
                      opacity: 0,
                      transition: 'all 0.3s',
                      visibility: 'hidden'
                    }}
                    className="delete-button"
                  >
                    <Icon
                      type="delete"
                      style={{
                        color: '#ff4d4f',
                        fontSize: '14px'
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveFile(image.id);
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </Form.Item>
        )}

        {otherFiles.length > 0 && (
          <Form.Item label="已上传文件" labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {otherFiles.map(file => (
                <div 
                  key={file.id}
                  style={{ 
                    position: 'relative',
                    padding: '8px 12px',
                    border: '1px solid #e8e8e8',
                    borderRadius: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}
                  className="file-container"
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', overflow: 'hidden' }}>
                    <Icon type={getFileIcon(file.fileType)} style={{ fontSize: '24px', color: '#1890ff' }} />
                    <a 
                      href={file.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      style={{ 
                        whiteSpace: 'nowrap', 
                        overflow: 'hidden', 
                        textOverflow: 'ellipsis',
                        maxWidth: '300px'
                      }}
                    >
                      {file.name}
                    </a>
                  </div>
                  <Icon
                    type="delete"
                    style={{
                      color: '#ff4d4f',
                      fontSize: '14px',
                      cursor: 'pointer'
                    }}
                    onClick={() => handleRemoveFile(file.id)}
                  />
                </div>
              ))}
            </div>
          </Form.Item>
        )}
      </>
    );
  };

  return (
    <>
      <Modal
        title={
          <div>
            添加批注
            <div style={{ fontSize: '12px', color: '#999', marginTop: '5px' }}>
              {/* {getSelectedNodesText()} */}
            </div>
          </div>
        }
        visible={visible}
        onCancel={handleClose}
        footer={[
          <Button key="cancel" onClick={handleClose}>
            取消
          </Button>,
          <Button 
            key="submit"
            type="primary" 
            onClick={handleSubmit}
            disabled={!selectedNode || (!comment.trim() && files.length === 0)}
          >
            确定
          </Button>
        ]}
        width={660}
        className="comment-modal"
        bodyStyle={{ maxHeight: '60vh', overflowY: 'auto' }}
      >
        <Form layout="horizontal">
          <Form.Item 
            label="批注" 
            required={true}
            style={{ marginBottom: 16 }}
            labelCol={{ span: 3 }}
            wrapperCol={{ span: 21 }}
          >
            <TextArea
              value={comment}
              onChange={e => setComment(e.target.value)}
              onPaste={handlePaste}
              placeholder="请输入批注内容，可直接粘贴图片"
              rows={4}
              autoSize={{ minRows: 4, maxRows: 8 }}
              disabled={!selectedNode}
            />
          </Form.Item>
          
          <Form.Item label="上传附件" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
            <Upload
              customRequest={customUploadRequest}
              showUploadList={false}
              disabled={!selectedNode}
              accept="*/*"
              beforeUpload={file => {
                if (file.size > 10 * 1024 * 1024) {
                  message.error('文件大小不能超过10MB');
                  return false;
                }
                return true;
              }}
            >
              <Button disabled={!selectedNode}>
                <Icon type="upload" /> 点击上传
              </Button>
            </Upload>
            <div style={{ fontSize: '12px', color: '#999', marginTop: '5px' }}>
              支持各种文件格式，图片可直接粘贴或点击上传，文件大小不超过10MB
            </div>
          </Form.Item>
          
          {renderFileList()}
        </Form>

        <style>
          {`
            .image-container {
              position: relative;
            }
            .image-container:hover .delete-button {
              opacity: 1 !important;
              visibility: visible !important;
            }
            .delete-button:hover {
              background: rgba(255, 255, 255, 0.95) !important;
            }
            .file-container:hover {
              background: #f9f9f9;
            }
          `}
        </style>
      </Modal>

      <Modal
        visible={previewVisible}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width={800}
        bodyStyle={{ 
          padding: '12px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px',
          background: '#f0f0f0'
        }}
      >
        <div style={{
          maxWidth: '100%',
          maxHeight: '80vh',
          overflow: 'auto',
          textAlign: 'center'
        }}>
          <img
            alt="预览图片"
            style={{ 
              maxWidth: '100%',
              maxHeight: '80vh',
              objectFit: 'contain'
            }}
            src={previewImage}
          />
        </div>
      </Modal>
    </>
  );
};

export default CommentDrawer; 