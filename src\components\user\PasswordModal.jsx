import React from 'react';
import { Modal, Form, Input, message } from 'antd';
import request from '@/utils/axios';
import { router } from 'umi';
const PasswordModal = ({ visible, onCancel, form }) => {
  const { getFieldDecorator, validateFields, resetFields } = form;
  const userInfo = JSON.parse(sessionStorage.getItem('userInfo')); 
  console.log('userInfo',userInfo);
  
  const handleOk = () => {
    validateFields(async (err, values) => {
      if (!err) {
        try {
          const response = await request('/user/updatePwd', {
            method: 'POST',
            body: {...values,id:userInfo.userId}
          });

          if (response && response.code === 200) {
            message.success('密码修改成功,请重新登录');
            resetFields();
            onCancel();
            setTimeout(() => {
            sessionStorage.clear();
            Cookies.remove('userName'); // 离线版本时，设置用户名兼容线上版本 以为线上版本取的是userName(有驼峰)
            Cookies.remove('username');
           // 获取当前hash路由路径
           const currentPath = window.location.hash.slice(1); // 去掉#号
           const redirectUrl = encodeURIComponent(currentPath);
           router.push(`/login?jumpto=${redirectUrl}`);
            }, 1000);
          } else {
            message.error(response?.msg || '密码修改失败');
          }
        } catch (error) {
          console.error('修改密码失败:', error);
          message.error('修改密码失败');
        }
      }
    });
  };

  return (
    <Modal
      title="修改密码"
      visible={visible}
      onOk={handleOk}
      onCancel={() => {
        resetFields();
        onCancel();
      }}
      okText="确认"
      cancelText="取消"
    >
      <Form layout="vertical">
        <Form.Item label="旧密码">
          {getFieldDecorator('oldPassword', {
            rules: [
              { required: true, message: '请输入旧密码' },
            //   { min: 6, message: '密码长度不能小于6位' }
            ],
          })(
            <Input.Password placeholder="请输入旧密码" />
          )}
        </Form.Item>
        <Form.Item label="新密码">
          {getFieldDecorator('password', {
            rules: [
              { required: true, message: '请输入新密码' },
            //   { min: 6, message: '密码长度不能小于6位' }
            ],
          })(
            <Input.Password placeholder="请输入新密码" />
          )}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create()(PasswordModal);