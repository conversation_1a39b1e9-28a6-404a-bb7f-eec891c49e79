.container {
  min-height: 100vh;
  // height: calc(100vh - 95px);
  background-repeat: no-repeat;
  background-size: cover;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.searchWrapper {
  width: 100%;
  max-width: 500px; // 调整搜索框宽度
  margin: 0 auto;
  padding: 40px 0;
  
  .searchInput {
    width: 100%;
    :global {
      .ant-input {
        height: 40px;
        border-radius: 4px 0 0 4px;
        border: 1px solid #e8e8e8;
        
        &:focus {
          box-shadow: none;
          border-color: #e8e8e8;
        }
        
        &::placeholder {
          color: #999;
        }
      }
      
      .ant-input-search-button {
        height: 40px;
        width: 40px;
        border-radius: 0 4px 4px 0;
        border: none;
        background: #00B96B; // 绿色按钮背景色
        
        &:hover {
          background: #00a65a; // hover时的颜色
        }
        
        .anticon {
          color: white;
        }
      }
    }
  }
}

.content {
  width: 1200px;
  // background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  // padding: 24px;
  margin: 0 auto;
  
  .header {
    margin-bottom: 24px;
    h2 {
      font-size: 18px;
      color: #333;
      span {
        font-size: 14px;
        color: #999;
        margin-left: 8px;
      }
    }
  }
}

.projectGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.projectCard {
  // background: #fff;
  // border-radius: 4px;
  // padding: 16px;
  // display: flex;
  // align-items: center;
  // box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  // cursor: pointer;
  // transition: all 0.3s;
  width: 384px;
  height: 120px;
  background: #ffffff;
  border-radius: 4px;
  // margin-bottom: 20px;
  -webkit-box-shadow: 0px 8px 17px 0px rgba(191, 220, 209, 0.35);
  box-shadow: 0px 8px 17px 0px rgba(191, 220, 209, 0.35);
  position: relative;
  margin-right: 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #ffffff;
  padding: 22px 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.12);
  }
  
  .projectIcon {
    width: 48px;
    height: 48px;
    margin-right: 16px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }
  
  .projectInfo {
    flex: 1;
    h3 {
      // margin: 0 0 8px;
      // font-size: 16px;
      // color: #333;
      font-size: 20px;
      color: #313238;
      line-height: 28px;
    }
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

.emptyProject {
  text-align: center;
  padding: 40px 0;
}