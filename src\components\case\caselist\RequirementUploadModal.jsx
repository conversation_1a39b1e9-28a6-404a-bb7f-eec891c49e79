/* eslint-disable */
import React from 'react';
import PropTypes from 'prop-types';
import {
  Upload,
  Form,
  message,
  Modal,
  Icon,
  Row,
  Col,
  TreeSelect,
} from 'antd';
const { Dragger } = Upload;
import './index.scss';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 16 },
};

const { TreeNode } = TreeSelect;

class RequirementUploadModal extends React.Component {
  static propTypes = {
    visible: PropTypes.bool,
    onCancel: PropTypes.func,
    onOk: PropTypes.func,
    productId: PropTypes.any,
    selectedCaseIds: PropTypes.array, // 当前选中的分类ID
    cardTree: PropTypes.array, // 分类树数据
    form: PropTypes.object,
  };

  constructor(props) {
    super(props);
    this.state = {
      requirementFile: null, // 保存上传的需求文件
    };
  }

  componentWillReceiveProps(nextProps) {
    if (!nextProps.visible) {
      this.props.form.resetFields();
      this.setState({
        requirementFile: null,
      });
    }
  }

  handleOk = () => {
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.submitRequirementUpload(values);
      }
    });
  };

  // 提交需求上传
  submitRequirementUpload = (values) => {
    const { requirementFile } = this.state;
    
    if (!requirementFile) {
      message.error('请上传需求信息文件');
      return;
    }

    // TODO: 调用后端接口上传需求
    console.log('上传需求参数:', {
      categoryIds: values.categoryIds,
      file: requirementFile,
      productId: this.props.productId,
    });

    // 模拟接口调用
    const formData = new FormData();
    formData.append('file', requirementFile);
    formData.append('categoryIds', values.categoryIds ? values.categoryIds.join(',') : '');
    formData.append('productId', this.props.productId);

    // TODO: 替换为真实接口
    // const url = `${this.props.apiPrefix}/requirement/upload`;
    // request(url, { method: 'POST', body: formData }).then(res => {
    //   if (res.code === 200) {
    //     message.success('需求上传成功');
    //     this.props.onOk && this.props.onOk();
    //   } else {
    //     message.error(res.msg || '需求上传失败');
    //   }
    // });

    // 模拟成功响应
    setTimeout(() => {
      message.success('需求上传成功');
      this.props.onOk && this.props.onOk();
    }, 1000);
  };

  // 渲染分类树节点
  renderTreeNodes = (data = []) =>
    data.map(item => {
      item.title = <span>{item.text}</span>;
      if (item.children) {
        return (
          <TreeNode
            title={item.title}
            value={item.id}
            key={item.id}
            dataRef={item}
          >
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return <TreeNode {...item} />;
    });

  render() {
    const { visible, onCancel, selectedCaseIds, cardTree } = this.props;
    const { requirementFile } = this.state;
    const { getFieldDecorator } = this.props.form;

    // 文件上传配置
    const uploadProps = {
      accept: '.xls,.xlsx',
      onRemove: file => {
        this.setState({ requirementFile: null });
      },
      beforeUpload: file => {
        // 检查文件类型
        const isExcel = /(?:xls|xlsx)$/i.test(file.name);
        if (!isExcel) {
          message.error('只能上传Excel文件(.xls, .xlsx)');
          return false;
        }

        // 检查文件大小
        const isLt10M = file.size / 1024 / 1024 <= 10;
        if (!isLt10M) {
          message.error('文件大小不能超过10M');
          return false;
        }

        this.setState({ requirementFile: file });
        return false;
      },
      fileList: requirementFile ? [requirementFile] : [],
    };

    return (
      <Modal
        visible={visible}
        onCancel={onCancel}
        onOk={this.handleOk}
        maskClosable={false}
        title="需求上传"
        okText="确认上传"
        cancelText="取消"
        width="600px"
        wrapClassName="requirement-upload-modal"
      >
        <Form.Item {...formItemLayout} label="需求分类：">
          {getFieldDecorator('categoryIds', {
            rules: [{ required: true, message: '请选择需求分类' }],
            initialValue: selectedCaseIds && selectedCaseIds.length > 0 
              ? (selectedCaseIds.length === 1 && selectedCaseIds[0] === 'root' ? [-1] : selectedCaseIds)
              : [-1],
          })(
            <TreeSelect
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              placeholder="请选择需求分类"
              allowClear
              treeDefaultExpandAll
              disabled
            >
              {this.renderTreeNodes(cardTree)}
            </TreeSelect>,
          )}
        </Form.Item>

        <Row style={{ marginBottom: '20px' }}>
          <Col span={6}>导入需求信息文件:</Col>
          <Col span={16} className="dragger">
            <div className="div-flex-child-1">
              <Dragger {...uploadProps}>
                {requirementFile === null ? (
                  <Icon
                    type="plus-circle"
                    style={{ color: '#447CE6', fontSize: '24px' }}
                  />
                ) : (
                  <Icon
                    type="file-excel"
                    style={{
                      color: '#447CE6',
                      fontSize: '24px',
                      position: 'relative',
                      top: '-15px',
                    }}
                  />
                )}
              </Dragger>
            </div>
            <div className="div-flex-child-2">
              <div>
                <span className="span-text span-text-bold">
                  上传Excel文件（必传）
                </span>
                <span className="span-text span-text-light">
                  支持.xls和.xlsx文件，请按照需求信息模板填写...
                </span>
              </div>
            </div>
          </Col>
        </Row>
      </Modal>
    );
  }
}

export default Form.create()(RequirementUploadModal);
