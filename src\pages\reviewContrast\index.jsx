import React from 'react';
import { Button, Icon, message, Dropdown, Menu, Tag, Empty } from 'antd';
import styles from './index.less';
import AgileTCEditor from '../../components/react-mindmap-editor';
import request from '@/utils/axios';
import AnnotationItem from '../../components/AnnotationItem';
import router from 'umi/router';
class ReviewPage extends React.Component {
  constructor(props) {
    super(props);
    this.statusMap = {
      '全部': '全部',
      '1': '未处理',
      '2': '同意',
      '3': '忽略'
    };
    this.state = {
      isAnnotationVisible: true,
      loading: false,
      info: null,
      annotations: [],
      selectedAnnotationId: null,
      filterStatus: '全部',
      filterStatusName: '全部',
      filterUser: '所有人',
      filterUserName: '所有人',
      userList: [], // 用户列表
      activeTab: 'review', // 新增：当前激活的标签（review/revise）
      reviewInfo: {},
      reviseInfo: {},
      caseInfo: {},
      productLineId: null,
      reviewId: undefined,
      status: 1, // 添加状态字段：1-review中，2-修订中，3-已完成
      compareData: [],
      reViewCompareData: {},
      reviseCompareData: {},
      showReviseButton: false,
      // reviewInfo: null,
      // reviseInfo: null,
      // caseInfo: null,

    };
    this.editorRef = React.createRef();
  }

  componentDidMount() {
    const { caseId, reviewId } = this.props.location.query;
    this.setState({ caseId, reviewId });
    this.getReviewInfo();
    this.fetchAnnotations();
    // this.getMindMapData();
    // this.fetchUserList();
    this.getReviewCompare();
  }
  getMindMapData = async () => {
    request(`/backup/getCaseDiff`, {
      method: 'GET',
      params: {
        caseId1: 1174,
        caseId2: 1168,
      },
    }).then(res => {
      this.setState({ loading: false });
      if (res.code === 200) {
        this.editorNode.setEditerData(res.data.content.root);
        //   this.setState({ info: res.data.backupinfo });
      } else {
        message.error(res.msg);
      }
    });
  };
  getReviewCompare = () => {
    const { reviewId } = this.props.location.query;
    request(`/review/reviewCompare`, {
      method: 'GET',
      params: { reviewId },
    }).then(res => {
      if (res.code === 200) {
        this.setState({ compareData: res.data });
        if (res.data.length === 1) {
          this.setState({ reViewCompareData: res.data[0], showReviseButton: false });
          this.editorNode.setEditerData(res.data[0].content.root);
          this.setState({ reviewInfo: {reviewer: res.data[0].optName, reviewTime: res.data[0].optTime } });
        }
        if (res.data.length === 2) {
          this.setState({ reViewCompareData: res.data[0], reviseCompareData: res.data[1], showReviseButton: true });
          this.editorNode.setEditerData(res.data[0].content.root);
          this.setState({ reviewInfo: {reviewer: res.data[0].optName, reviewTime: res.data[0].optTime } });
          this.setState({ reviseInfo: {reviser: res.data[1].optName, reviseTime: res.data[1].optTime } });
        }

      } else {
        message.error(res.msg);
      }
    });
  };
  handleNodeSelect = nodeId => {
    // 当节点被选中时,更新 selectedAnnotationId
    this.setState({ selectedAnnotationId: nodeId });

    // 找到对应的批注
    const annotation = this.state.annotations.find(
      item => item.nodeId === nodeId,
    );
    if (annotation) {
      // 如果找到对应的批注,滚动到视图中
      const element = document.getElementById(`annotation-${nodeId}`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }
  };
  fetchAnnotations = async () => {
    this.setState({ annotationsLoading: true }); // 开始加载时设置状态
    const { caseId, reviewId } = this.props.location.query;
    const { filterStatus, filterUser } = this.state;
    
    const params = {
      pageNum: 1,
      pageSize: 99999,
      reviewId: reviewId,
    };

    // 只有当不是默认值时才添加筛选参数
    if (filterStatus !== '全部') {
      params.status = filterStatus;
    }
    if (filterUser !== '所有人') {
      params.creator = filterUser;
    }

    request(`/comment/queryByPage`, {
      method: 'GET',
      params: params,
    })
      .then(res => {
        if (res.code === 200) {
          const arr = res.data.dataSources.map(item => {
            return {
              id: item.nodeId,
              nodeId: item.nodeId,
              nodeTitle: item.nodeName,
              nodeStatus: item.nodeStatus,
              comments:
                item.comments?.map(comment => {
                  return {
                    id: comment.id,
                    username: comment.creatorName,
                    content: comment.comment,
                    time: comment.gmtCreated,
                    status: comment.status,
                    statusName: comment.statusName,
                    images:comment.commentFile?.filter(file => {
                      // 判断文件是否为图片(通过扩展名判断)
                      const fileName = file.fileName.toLowerCase();
                      return fileName.endsWith('.png') || 
                             fileName.endsWith('.jpg') || 
                             fileName.endsWith('.jpeg') || 
                             fileName.endsWith('.gif') || 
                             fileName.endsWith('.bmp') || 
                             fileName.endsWith('.webp');
                    }).map(file => {
                      return {
                        id:file.id,
                        url:file.filePath,
                        name:file.fileName,
                      }
                    }) || [],
                    files:comment.commentFile?.filter(file => {
                      // 判断文件是否不是图片
                      const fileName = file.fileName.toLowerCase();
                      return !(fileName.endsWith('.png') || 
                             fileName.endsWith('.jpg') || 
                             fileName.endsWith('.jpeg') || 
                             fileName.endsWith('.gif') || 
                             fileName.endsWith('.bmp') || 
                             fileName.endsWith('.webp'));
                    }).map(file => {
                      return {
                        id:file.id,
                        url:file.filePath,
                        name:file.fileName,
                      }
                    }) || [],
                    replies:
                      (this.state.activeTab === 'review' ? [] : comment.commentDiscuss?.map(reply => {
                        return {
                          id: reply.id,
                          username: reply.creatorName,
                          content: reply.discuss,
                          time: reply.gmtCreated,
                        };
                      })) || [],
                  };
                }) || [],
            };
          }).sort((a, b) => {
            // 将nodeStatus=1的排到后面
            if (a.nodeStatus === 1 && b.nodeStatus !== 1) {
              return 1;
            }
            if (a.nodeStatus !== 1 && b.nodeStatus === 1) {
              return -1;
            }
            return 0;
          });
          this.setState({
            annotations: arr,
            annotationsLoading: false,
          });
        } else {
          message.error('获取批注失败');
          this.setState({ annotationsLoading: false });
        }
      })
      .catch(() => {
        message.error('获取批注失败');
        this.setState({ annotationsLoading: false });
      });
  };
  // 获取用户列表
  fetchUserList = async () => {
    request(`/user/companyUserList`, {
      method: 'GET',
      params: {
        id: this.state.productLineId,
      },
    })
      .then(res => {
        if (res.code === 200) {
          const userList = (res.data || []).map(user => ({
            name: user.username,
            id: user.id,
          }));

          this.setState({
            userList,
          });
        } else {
          console.error('获取用户列表失败:', res);
          message.error('获取用户列表失败: ' + (res.msg || '未知错误'));
        }
      })
      .catch(err => {
        console.error('获取用户列表出错:', err);
        message.error('获取用户列表失败，请检查网络连接');
        this.setState({ loading: false });
      });
  };

  handleAnnotationEdit = async (annotationId, commentId, content) => {
    try {
      const params = {
        id: commentId,
        comment: content,
      };

      request(`/comment/update`, {
        method: 'POST',
        body: params,
      })
        .then(res => {
          if (res.code === 200) {
            // this.setState({ annotations: updatedAnnotations });
            message.success('评论更新成功');
            this.fetchAnnotations();
          } else {
            message.error(res.msg);
          }
        })
        .catch(() => {
          message.error('评论更新失败');
        });
    } catch (error) {
      message.error('评论更新失败');
    }
  };

  handleAnnotationDelete = async (annotationId, commentId) => {
    try {
      request(`/comment/delete`, {
        method: 'POST',
        body: {
          id: commentId,
        },
      })
        .then(res => {
          if (res.code === 200) {
            message.success('删除成功');
            this.fetchAnnotations(); // 重新获取批注列表
          } else {
            message.error(res.msg);
          }
        })
        .catch(() => {
          message.error('删除失败');
        });
    } catch (error) {
      message.error('删除失败');
    }
  };

  handleAnnotationClick = id => {
    this.setState({ selectedAnnotationId: id });
    // // 这里需要调用脑图组件的方法来选中对应节点
    const annotation = this.state.annotations.find(item => item.nodeId === id);
    if (annotation && this.editorNode) {
      // 调用脑图组件的选中节点方法
      this.editorNode.selectNodeById(annotation.nodeId);
    }
  };

  toggleAnnotation = () => {
    this.setState(prevState => ({
      isAnnotationVisible: !prevState.isAnnotationVisible,
    }));
  };

  handleFilterChange = (type, value) => {
    if (type === 'filterUser') {
      if (value === '所有人') {
        this.setState({ 
          filterUser: '所有人',
          filterUserName: '所有人'
        }, () => {
          this.fetchAnnotations();
        });
      } else {
        const user = this.state.userList.find(u => u.id === value);
        this.setState({ 
          filterUser: value,  // 保存ID
          filterUserName: user ? user.name : value  // 保存名字
        }, () => {
          this.fetchAnnotations();
        });
      }
    } else if (type === 'filterStatus') {
      // 处理状态筛选
      const statusName = this.statusMap[value] || value;
      this.setState({ 
        filterStatus: value,
        filterStatusName: statusName
      }, () => {
        this.fetchAnnotations();
      });
    } else {
      this.setState({ [type]: value }, () => {
        this.fetchAnnotations();
      });
    }
  };

  getFilteredAnnotations = () => {
    // 直接返回annotations，因为筛选已经在接口层完成
    return this.state.annotations;
  };
  getReviewInfo = () => {
    const { reviewId } = this.props.location.query;
    request(`/review/getReviewById`, {
      method: 'GET',
      params: { id: reviewId },
    }).then(res => {
      if (res.code === 200) {
        this.setState({ reviewDetail: res.data });
        this.setState({ caseInfo: res.data });
        this.setState({ productLineId: this.state.caseInfo.productLineId });
        if (this.state.caseInfo.productLineId) {
          this.fetchUserList();
        }
      }
    });
  };
  // 回复批注内容
  handleAnnotationReply = (commentId, replyId, content) => {
    const params = {
      // commentId: commentId,
      commentId: replyId,
      discuss: content,
    };
    request(`/commentDiscuss/insert`, {
      method: 'POST',
      body: params,
    })
      .then(res => {
        if (res.code === 200) {
          // this.setState({ annotations: updatedAnnotations });
          message.success('评论成功');
          this.fetchAnnotations();
        } else {
          message.error(res.msg);
        }
      })
      .catch(() => {
        message.error('评论更新失败');
      });
  };
  // 删除回复内容
  handleReplyDelete = (replyId) => {
    try {
      request(`/commentDiscuss/delete`, {
        method: 'POST',
        body: {
          id: replyId,
        },
      })
        .then(res => {
          if (res.code === 200) {
            message.success('删除成功');
            this.fetchAnnotations(); // 重新获取批注列表
          } else {
            message.error(res.msg);
          }
        })
        .catch(() => {
          message.error('删除失败');
        });
    } catch (error) {
      message.error('删除失败');
    }
  };
  // 编辑回复内容
  handleReplyEdit = ( replyId,commentId, content) => {
    const params = {
      id: replyId,
      discuss: content,
    };
    request(`/commentDiscuss/update`, {
      method: 'POST', 
      body: params,
    })
      .then(res => {
        if (res.code === 200) {
          message.success('编辑成功');
          this.fetchAnnotations();  
        } else {
          message.error(res.msg);
        }
      })
      .catch(() => {
        message.error('编辑失败');
      });
  };

  handleTabChange = (tab) => {
    this.setState({ activeTab: tab }, () => {
      if (tab === 'review') {
        this.editorNode.setEditerData(this.state.reViewCompareData.content.root);
      } else if (tab === 'revise') {
        this.editorNode.setEditerData(this.state.reviseCompareData.content.root);
      }

      // 重置所有过滤条件
      this.setState({
        // 重置选择的批注id
        selectedAnnotationId: null,
        // 重置状态过滤
        filterStatus: '全部',
        filterStatusName: '全部',
        // 重置用户过滤
        filterUser: '所有人',
        filterUserName: '所有人',
        // 重置批注列表
        annotations: [],
        // 重置加载状态
        annotationsLoading: false
      }, () => {
        // 重新获取批注数据
        this.fetchAnnotations();
      });
    });
  };

  handleDeleteImage = async (imageId) => {
    try {
      const response = await request('/commentFile/delete', {
        method: 'POST',
        body: {
          id: imageId
        },
      });
      
      if (response.code === 200) {
        message.success('图片删除成功');
        this.fetchCompareData();
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      message.error('删除图片失败，请重试');
    }
  };

  // 删除附件
  handleDeleteFile = async (fileId) => {
    try {
      const response = await request('/commentFile/delete', {
        method: 'POST',
        body: {
          id: fileId
        },
      });
      
      if (response.code === 200) {
        message.success('附件删除成功');
        this.fetchCompareData();
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      message.error('删除附件失败，请重试');
    }
  };

  render() {
    const {
      isAnnotationVisible,
      selectedAnnotationId,
      filterStatus,
      filterUser,
      userList,
      activeTab,
      reviewInfo,
      reviseInfo,
      caseInfo,
      showReviseButton,
      reviewDetail,
    } = this.state;
    const filteredAnnotations = this.getFilteredAnnotations();

    return (
      <div className={styles.revise_container}>
        <div className={styles.header}>
          <div className={styles.left}>
            <Icon
              type="left"
              className={styles.backIcon}
              onClick={() => {             
                if(reviewDetail?.companyId){
                  router.push(`/review/reviewList/${reviewDetail.companyId}`);
                }else{
                  router.push(`/`);
                }
              }}
            />
            <div className={styles.titleWrapper}>
              <div className={styles.titleSection}>
                <span className={styles.caseName}>{caseInfo?.title}(id:{caseInfo?.id})</span>
                <Tag color={
                  caseInfo?.status === 1 ? 'blue' : 
                  caseInfo?.status === 2 ? 'orange' : 
                  caseInfo?.status === 3 ? 'green' : 'default'
                }>
                  {caseInfo?.status === 1 ? 'Review中' : 
                   caseInfo?.status === 2 ? '修订中' : 
                   caseInfo?.status === 3 ? '已完成' : '未知状态'}
                </Tag>
                {/* {caseInfo.tags.map((tag, index) => (
                  <span key={index} className={styles.tag}>
                    {tag}
                  </span>
                ))} */}
              </div>
              <div className={styles.tabButtons}>
                <Button
                  type={activeTab === 'review' ? 'primary' : 'default'}
                  onClick={() => this.handleTabChange('review')}
                >
                  Review后
                </Button>
                {showReviseButton && (
                  <Button
                    type={activeTab === 'revise' ? 'primary' : 'default'}
                    onClick={() => this.handleTabChange('revise')}
                  >
                    修订后
                  </Button>
                )}
              </div>
            </div>
          </div>
          <div className={styles.center}>
            <div className={styles.infoWrapper}>
              {/* <div className={styles.infoRow}>
                <span className={styles.label}>创建人：</span>
                <span className={styles.value}>siliangjiang002</span>
              </div>
              <div className={styles.infoRow}>
                <span className={styles.label}>创建时间：</span>
                <span className={styles.value}>2025-03-06 06:06:46</span>
              </div> */}
              {activeTab === 'review' ? (
                <>
                  <div className={styles.infoRow}>
                    <span className={styles.label}>Review人：</span>
                    <span className={styles.value}>{reviewInfo.reviewer?reviewInfo.reviewer:'-'}</span>
                  </div>
                  <div className={styles.infoRow}>
                    <span className={styles.label}>Review完成时间：</span>
                    <span className={styles.value}>
                      {reviewInfo.reviewTime?reviewInfo.reviewTime:'-'}
                    </span>
                  </div>
                </>
              ) : (
                <>
                  <div className={styles.infoRow}>
                    <span className={styles.label}>修订人：</span>
                    <span className={styles.value}>{reviseInfo.reviser?reviseInfo.reviser:'-'}</span>
                  </div>
                  <div className={styles.infoRow}>
                    <span className={styles.label}>修订完成时间：</span>
                    <span className={styles.value}>
                      {reviseInfo.reviseTime?reviseInfo.reviseTime:'-'}
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>
          <div className={styles.right}>
            <div className={styles.colorLegend}>
              <div className={styles.legendTitle}>颜色标识</div>
              <div className={styles.legendItems}>
                {/* <Tag style={{ backgroundColor: '#fff9c4', border: 'none' }}>
                  该节点有新批注
                </Tag> */}
                <Tag style={{ backgroundColor: '#ddfade', border: 'none' }}>
                  该节点被添加
                </Tag>
                <Tag style={{ backgroundColor: '#ffebee', border: 'none' }}>
                  该节点被删除
                </Tag>
                <Tag style={{ backgroundColor: '#e3f2fd', border: 'none' }}>
                  内容已变更
                </Tag>
              </div>
            </div>
          </div>
        </div>

        <div className={styles.revise_content}>
          <div className={styles.mindmap}>
            <AgileTCEditor
              ref={editorNode => (this.editorNode = editorNode)}
              tags={['前置条件', '执行步骤', '预期结果']}
              readOnly={true}
              mediaShow={true}
              editorStyle={{ height: '100%' }}
              toolbar={{
                image: true,
                theme: ['classic-compact', 'fresh-blue', 'fresh-green-compat'],
                template: ['default', 'right', 'fish-bone'],
                noteTemplate: '# test',
              }}
              type="compare"
              onNodeSelect={this.handleNodeSelect}
            />
          </div>
          <div className={styles.annotationContainer}>
            <div className={styles.collapseBtn} onClick={this.toggleAnnotation}>
              <Icon type={isAnnotationVisible ? 'right' : 'left'} />
            </div>
            <div
              className={`${styles.annotation} ${
                !isAnnotationVisible ? styles.collapsed : ''
              }`}
            >
              <div className={styles.annotationHeader}>
                <div className={styles.title}>批注列表</div>
                <div className={styles.filter}>
                  <Dropdown
                    overlay={
                      <Menu
                        selectedKeys={[filterUser === '所有人' ? '所有人' : userList.find(u => u.id === filterUser)?.name || filterUser]}
                        onClick={({ key }) =>
                          this.handleFilterChange('filterUser', key === '所有人' ? '所有人' : userList.find(u => u.name === key)?.id || key)
                        }
                      >
                        <Menu.Item key="所有人">所有人</Menu.Item>
                        {userList.map(user => (
                          <Menu.Item key={user.name}>{user.name}</Menu.Item>
                        ))}
                      </Menu>
                    }
                    trigger={['click']}
                  >
                    <span className={styles.filterDropdown}>
                      {this.state.filterUserName} <Icon type="down" />
                    </span>
                  </Dropdown>
                  <Dropdown
                    overlay={
                      <Menu
                        selectedKeys={[filterStatus]}
                        onClick={({ key }) =>
                          this.handleFilterChange('filterStatus', key)
                        }
                      >
                        <Menu.Item key="全部">全部</Menu.Item>
                        <Menu.Item key="1">未处理</Menu.Item>
                        <Menu.Item key="2">同意</Menu.Item>
                        <Menu.Item key="3">忽略</Menu.Item>
                      </Menu>
                    }
                    trigger={['click']}
                  >
                    <span className={styles.filterDropdown}>
                      {this.state.filterStatusName} <Icon type="down" />
                    </span>
                  </Dropdown>
                </div>
              </div>
              <div className={styles.annotationList}>
                {filteredAnnotations.length > 0 ? (
                  filteredAnnotations.map(annotation => (
                    <AnnotationItem
                      key={annotation.id}
                      data={annotation}
                      onEdit={this.handleAnnotationEdit}
                      onDelete={this.handleAnnotationDelete}
                      onReply={this.handleAnnotationReply}
                      isSelected={selectedAnnotationId === annotation.nodeId}
                      onClick={this.handleAnnotationClick}
                      pageType="contrast"
                      minder={this.editorNode.minder}
                      onDeleteImage={this.handleDeleteImage}
                      onDeleteFile={this.handleDeleteFile}
                    />
                  ))
                ) : (
                  <div className={styles.emptyState}>
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="暂无批注"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default ReviewPage;
