/* eslint-disable */
export default {
  getQueryString(name) {
    let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
    let r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
  },
  setcookie(name, value) {
    let Days = 30;
    let exp = new Date();
    exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
    document.cookie =
      name + '=' + escape(value) + ';expires=' + exp.toGMTString();
  },
  // 获取hash参数的方法
  getHashQueryString(name) {
    const hash = window.location.hash;
    const search = hash.split('?')[1];
    if (!search) return null;
    
    const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
    const r = search.match(reg);
    if (r != null) return decodeURIComponent(r[2]);
    return null;
  }
};
