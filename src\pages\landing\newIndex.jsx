import React, { useState, useEffect } from 'react';
import { Input, Card, Row, Col, Empty, message } from 'antd';
import router from 'umi/router';
import request from '@/utils/axios'; // 导入封装的request
// import Cookies from 'js-cookie';
import Headers from '../../layouts/header.js';
import styles from './less/newIndex.less';
import defaultLogo from '@/assets/images/default-logo.png';
import bgImage from '@/assets/images/home_bg.9bf7acb.png';

// ProjectCard 组件
const ProjectCard = ({ project }) => {
  const { code, remark, logo, id,name } = project;

  const handleClick = () => {
    // 将项目信息存储到 sessionStorage
    // sessionStorage.setItem('currentProject', JSON.stringify({
    //   id,
    //   name: channel,
    //   description
    // }));
    sessionStorage.setItem('currentProjectId', id);
    router.push(`/case/caseList/${id}`);
    // 修改为新开窗口
    // const url = `/case/caseList/${id}`
    // window.open(url, '_blank')
  };

  return (
    <div
      className={styles.projectCard}
      onClick={handleClick}
    >
      <div className={styles.projectIcon}>
        <img src={logo || defaultLogo} alt={code} />
      </div>
      <div className={styles.projectInfo}>
        <h3>{name}</h3>
        <p>{remark}</p>
      </div>
    </div>
  );
};

const EmptyProject = () => (
  <div className={styles.emptyProject}>
    <Empty
      image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
      imageStyle={{
        height: 160,
      }}
      description="暂无项目,请联系管理员创建项目"
    />
  </div>
);

const HomePage = () => {
  const [projectList, setProjectList] = useState([]);
  const [searchText, setSearchText] = useState('');
  // 模拟获取项目列表
  const fetchProjects = async () => {
    try {
      const response = await request('/company/listByUserId', {
        method: 'GET',
        params: {
          pageNum: 1,
          pageSize: 9999,
          // name: params.name,
        },
      });

      if (response && response.code === 200) {
        setProjectList(response.data || []);
      } else {
        message.error(response?.msg || '获取数据失败');
        setProjectList(response.data || []);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error('获取项目列表失败');
      setProjectList([]);
    }
  };

  useEffect(() => {
    fetchProjects();
    sessionStorage.removeItem('currentProjectId');
  }, []);

  // 搜索过滤
  const filteredProjects = projectList.filter(
    project =>
      project.name.toLowerCase().includes(searchText.toLowerCase()) ||
      project.remark.toLowerCase().includes(searchText.toLowerCase()),
  );

  return (
    <section>
      {/* <Headers /> */}
      <div
        className={styles.container}
        style={{ backgroundImage: `url(${bgImage})` }}
      >
        <div className={styles.searchWrapper}>
          <Input.Search
            placeholder="请输入项目名称"
            enterButton
            size="large"
            className={styles.searchInput}
            onChange={e => setSearchText(e.target.value)}
          />
        </div>

        <div className={styles.content}>
          <div className={styles.header}>
            <h2>
              我的项目 <span>{filteredProjects.length}个</span>
            </h2>
          </div>

          {filteredProjects.length === 0 ? (
            <EmptyProject />
          ) : (
            <div className={styles.projectGrid}>
              {filteredProjects.map(project => (
                <ProjectCard key={project.id} project={project} />
              ))}              
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default HomePage;
