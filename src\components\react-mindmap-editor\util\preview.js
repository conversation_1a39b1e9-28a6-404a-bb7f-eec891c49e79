export default function preview(node, previewNode) {
  const icon = node.getRenderer('NoteIconRenderer').getRenderShape();
  const box = icon.getRenderBox('screen');
  const $previewer = previewNode;
  const $container = document.getElementsByClassName('kityminder-core-container');
  const $outerContainer = document.getElementsByClassName('kityminder-editor-container');
  const $annotation = document.getElementsByClassName('annotation')[0];

  $previewer.scrollTop = 0;

  // 获取批注列表的宽度
  const annotationWidth = $annotation ? ($annotation.classList.contains('collapsed') ? 0 : $annotation.offsetWidth) : 0;

  // 调整x坐标计算,考虑批注列表的宽度
  let x = box.left - (document.documentElement.clientWidth - $container[0].clientWidth - annotationWidth);
  let y = box.bottom + 8 - $outerContainer[0].getBoundingClientRect().top;

  $previewer.style.left = Math.round(x) + 'px';
  $previewer.style.top = Math.round(y) + 'px';
}
