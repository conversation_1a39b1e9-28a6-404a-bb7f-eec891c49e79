import axios from 'axios';
import { notification, Modal } from 'antd';
import Cookies from 'js-cookie';
import { router } from 'umi';
/**
 * 一、功能：
 * 1. 统一拦截http错误请求码；
 * 2. 统一拦截业务错误代码；
 * 3. 统一设置请求前缀
 * |-- 每个 http 加前缀 baseURL = /api/v1，从配置文件中获取 apiPrefix

 * 
 * 二、引包：
 * |-- axios：http 请求工具库
 * |-- notification：Antd组件 > 处理错误响应码提示信息
 * |-- routerRedux：dva/router对象，用于路由跳转，错误响应码跳转相应页面
 * |-- store：dva中对象，使用里面的 dispatch 对象，用于触发路由跳转
 */
let isShowingLoginModal = false;
const { NODE_ENV } = process.env;
// console.log('env', NODE_ENV);
window.apiPrefix = NODE_ENV === 'production' ? '/thirdapp/ecocase/api' : '/api';
// 设置全局参数，如响应超时时间5min，请求前缀等。
axios.defaults.timeout = 1000 * 60 * 5;
// axios.defaults.baseURL = window.apiPrefix
axios.defaults.withCredentials = true;
// 状态码错误信息
const codeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

// 添加一个请求拦截器，用于设置请求过渡状态
axios.interceptors.request.use(
  config => {
    config.baseURL = window.apiPrefix;
    config.headers = {
      ...config.headers,
      // 'X-Auth-Openid': 'siliangjiang002',
      // 'X-Auth-Username': 'siliangjiang002',
      // 'X-Auth-Corpid': 'tx1234546',
      // 'X-Auth-Company': 'TX',
      // 'X-Auth-Viscompanys': 'ZDJX,ZDZD',
      // 'X-Auth-Roles': 'ecocase_admin,feedback_admin',
    };
    return config;
  },
  error => {
    return Promise.reject(error);
  },
);

// 添加一个返回拦截器
axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    // 即使出现异常，也要调用关闭方法，否则一直处于加载状态很奇怪
    return Promise.reject(error);
  },
);

export default function request(url, opt) {
  // 调用 axios api，统一拦截
  const options = {};
  options.method = opt !== undefined ? opt.method : 'get';
  if (opt) {
    if (opt.body) {
      options.data =
        typeof opt.body === 'string' ? JSON.parse(opt.body) : opt.body;
    }

    // 支持设置responseType，用于文件下载等场景
    if (opt.responseType) {
      options.responseType = opt.responseType;
    }

    // if (opt.headers) options.headers = { 'Content-Type': 'application/x-www-form-urlencoded' }
    if (opt.params !== undefined) {
      url += '?';
      for (let key in opt.params) {
        if (opt.params[key] !== undefined && opt.params[key] !== '') {
          url = url + key + '=' + opt.params[key] + '&';
        }
      }
      url = url.substring(0, url.length - 1);
    }
  }
  return axios({
    url,
    ...options,
  })
    .then(response => {
      // >>>>>>>>>>>>>> 请求成功 <<<<<<<<<<<<<<
      // console.log(`【${opt.method} ${opt.url}】请求成功，响应数据：`, response)

      // 如果是blob响应类型，返回包含文件数据和文件名的对象
      if (opt && opt.responseType === 'blob') {
        // 从响应头中获取文件名
        let fileName = 'download';
        let fileExtension = '';
        let mimeType = '';

        const contentDisposition = response.headers['content-disposition'];
        if (contentDisposition) {
          const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
          if (fileNameMatch && fileNameMatch[1]) {
            fileName = fileNameMatch[1].replace(/['"]/g, '');
            // 处理URL编码的文件名
            try {
              fileName = decodeURIComponent(fileName);
            } catch (e) {
              // 如果解码失败，使用原始文件名
            }

            // 从文件名中提取扩展名
            const extensionMatch = fileName.match(/\.([^.]+)$/);
            if (extensionMatch) {
              fileExtension = extensionMatch[1].toLowerCase();
            }
          }
        }

        // 从响应头中获取MIME类型
        mimeType = response.headers['content-type'] || '';

        return {
          data: response.data,
          fileName: fileName,
          fileExtension: fileExtension,
          mimeType: mimeType,
          headers: response.headers,
          status: response.status
        };
      }

      // 打印业务错误提示
      // if (response.data && response.data.code != '0000') {
      //   message.error(response.data.message)
      // }
      // eslint-disable-next-line
      if (response.data.code === 401 || response.code === 401) {
        let loginPath = `${response.data.data.login_url}?app_id=${response.data.data.app_id}`;
        let pagePath = encodeURIComponent(window.location.href);
        const redirectURL = `${loginPath}&version=1.0&jumpto=${pagePath}`;
        window.location.href = redirectURL;
        return Promise.reject(new Error('服务不可用，请联系管理员'));
      }
      // >>>>>>>>>>>>>> 当前未登录 <<<<<<<<<<<<<<
      if (response.data.code === 99993 || response.code === 99993) {
        const redirectURL = `/login`;
        window.location.href = redirectURL;
        return Promise.reject(new Error('服务不可用，请联系管理员'));
      }
      // >>>>>>>>>>>>>> 离线版本下当前未登录 <<<<<<<<<<<<<<
      if ((response.data.code === 100011 || response.code === 100011) && process.env.APP_VERSION === 'offline') {
          // 如果已经在显示登录弹窗，则直接返回
          if (isShowingLoginModal) {
            return Promise.reject(new Error('获取登录信息失败，请先重新登录!'));
          }
        // 清除会话信息
        sessionStorage.clear();
        Cookies.remove('username');
        Cookies.remove('userName');
         // 设置标志位
         isShowingLoginModal = true;
          // 使用Modal显示提示信息
          Modal.confirm({
            title: '登录已失效',
            content: '您的登录信息已失效，请重新登录',
            okText: '重新登录',
            cancelText: '取消',
            onOk: () => {
              // 获取当前路径用于重定向
              const currentPath = window.location.hash.slice(1);
              const redirectUrl = encodeURIComponent(currentPath);
              // 跳转到登录页
              router.push(`/login?jumpto=${redirectUrl}`);
            },
            afterClose: () => {
              // 弹窗关闭后重置标志位
              isShowingLoginModal = false;
            }
          });
        return Promise.reject(new Error('获取登录信息失败，请先重新登录!'));
      }
      return { ...response.data };
    })
    .catch(error => {
      // >>>>>>>>>>>>>> 请求失败 <<<<<<<<<<<<<<
      // 请求配置发生的错误
      if (!error.response) {
        // eslint-disable-next-line
        return console.log('Error', error.message);
      }

      // 响应时状态码处理
      const status = error.response.status;
      const errortext = codeMessage[status] || error.response.statusText;

      notification.error({
        message: `请求错误 ${status}`,
        description: errortext,
      });

      return { code: status, message: errortext };
    });
}
