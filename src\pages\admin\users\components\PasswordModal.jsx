// src/pages/admin/users/components/PasswordModal.jsx

import React from 'react';
import { Modal, Form, Input } from 'antd';

const PasswordModal = ({ visible, onCancel, onOk, confirmLoading, form, username }) => {
  const { getFieldDecorator } = form;

  const handleOk = () => {
    form.validateFields((err, values) => {
      if (!err) {
        onOk(values.password);
      }
    });
  };

  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  return (
    <Modal
      title="重置密码"
      visible={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={confirmLoading}
      okText="确定"
      cancelText="取消"
    >
      <Form {...formItemLayout}>
        <Form.Item label="用户名">
          <span>{username}</span>
          <span style={{ color: 'red', marginLeft: '10px' }}>重置后密码默认为：88888888</span>
        </Form.Item>
        {/* <Form.Item label="新密码">
          {getFieldDecorator('password', {
            rules: [
              { required: true, message: '请输入新密码' },
              // { min: 6, message: '密码长度不能小于6位' }
            ],
          })(
            <Input.Password 
              placeholder="请输入新密码"
              autoComplete="new-password"
            />
          )}
        </Form.Item> */}
      </Form>
    </Modal>
  );
};

export default Form.create()(PasswordModal);