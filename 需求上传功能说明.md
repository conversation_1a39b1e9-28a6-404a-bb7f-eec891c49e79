# 需求上传功能说明

## 功能概述

在需求视图模式下，原有的"新建用例"按钮变更为"需求上传"按钮，点击后弹出需求上传弹窗，支持用户上传需求信息Excel文件并选择需求分类。

## 功能实现详情

### 1. 按钮动态切换

#### 按钮文本和图标
- **默认视图**: "新建用例" + plus图标
- **需求视图**: "需求上传" + upload图标

#### 按钮功能
- **默认视图**: 打开新建用例弹窗
- **需求视图**: 打开需求上传弹窗

```javascript
<Button
  type="primary"
  onClick={() => {
    if (this.state.viewMode === 'requirement') {
      // 需求视图时显示需求上传弹窗
      this.showRequirementUploadModal();
    } else {
      // 默认视图时显示新建用例弹窗
      this.handleTask('add');
      this.setState({
        currCase: null,
        visible: true,
        project: null,
        requirement: null,
      });
    }
  }}
>
  <Icon type={this.state.viewMode === 'requirement' ? 'upload' : 'plus'} /> 
  {this.state.viewMode === 'requirement' ? '需求上传' : '新建用例'}
</Button>
```

### 2. 需求上传弹窗组件

#### 组件文件
- **文件路径**: `src/components/case/caselist/RequirementUploadModal.jsx`
- **基于**: 复用新建用例弹窗的设计模式和样式

#### 表单项详情

##### 需求分类表单
- **复用来源**: 新建用例弹窗中的"用例分类"表单项
- **组件类型**: TreeSelect (多选树形选择器)
- **默认值**: 当前左侧选择的分类ID
- **验证规则**: 必填项
- **功能**: 支持多选，树形展示，默认展开所有节点

```javascript
<TreeSelect
  style={{ width: '100%' }}
  dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
  placeholder="请选择需求分类"
  allowClear
  multiple
  treeDefaultExpandAll
>
  {this.renderTreeNodes(cardTree)}
</TreeSelect>
```

##### 导入需求信息文件表单
- **参考样式**: 新建用例弹窗中的"导入用例文件"样式
- **文件限制**: 只接受Excel文件(.xls, .xlsx)
- **文件大小**: 最大10M
- **必填性**: 必须上传文件
- **组件**: Ant Design的Dragger上传组件

```javascript
const uploadProps = {
  accept: '.xls,.xlsx',
  beforeUpload: file => {
    const isExcel = /(?:xls|xlsx)$/i.test(file.name);
    if (!isExcel) {
      message.error('只能上传Excel文件(.xls, .xlsx)');
      return false;
    }
    const isLt10M = file.size / 1024 / 1024 <= 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10M');
      return false;
    }
    return false;
  },
};
```

### 3. 数据流和状态管理

#### 新增状态
```javascript
state = {
  requirementUploadVisible: false, // 需求上传弹窗显示状态
  cardTree: [], // 分类树数据
  // ... 其他状态
}
```

#### 核心方法

##### 获取分类树数据
```javascript
getCardTree = () => {
  request(`${doneApiPrefix}/dir/cardTree`, {
    method: 'GET',
    params: {
      productLineId: productLineId,
      channel: 1,
      companyId: productLineId,
    },
  }).then(res => {
    this.setState({ cardTree: res.data ? res.data.children : [] });
  });
};
```

##### 弹窗控制方法
```javascript
// 显示需求上传弹窗
showRequirementUploadModal = () => {
  this.setState({ requirementUploadVisible: true });
};

// 关闭需求上传弹窗
hideRequirementUploadModal = () => {
  this.setState({ requirementUploadVisible: false });
};

// 需求上传成功回调
handleRequirementUploadSuccess = () => {
  this.setState({ requirementUploadVisible: false });
  this.getCaseList(); // 刷新列表数据
  message.success('需求上传成功');
};
```

### 4. 接口预留和Mock实现

#### 接口预留
```javascript
// TODO: 替换为真实接口
// const url = `${this.props.apiPrefix}/requirement/upload`;
// request(url, { method: 'POST', body: formData }).then(res => {
//   if (res.code === 200) {
//     message.success('需求上传成功');
//     this.props.onOk && this.props.onOk();
//   } else {
//     message.error(res.msg || '需求上传失败');
//   }
// });
```

#### Mock实现
```javascript
// 模拟接口调用
const formData = new FormData();
formData.append('file', requirementFile);
formData.append('categoryIds', values.categoryIds.join(','));
formData.append('productId', this.props.productId);

// 模拟成功响应
setTimeout(() => {
  message.success('需求上传成功');
  this.props.onOk && this.props.onOk();
}, 1000);
```

### 5. 用户体验设计

#### 智能默认值
- 弹窗打开时自动选择当前左侧树选中的分类
- 如果选中的是根节点，则默认选择[-1]

#### 文件验证
- 实时验证文件类型，只允许Excel文件
- 文件大小限制，超过10M时提示错误
- 上传前验证，防止无效文件提交

#### 视觉反馈
- 上传成功后显示成功提示
- 文件选择后显示Excel图标
- 表单验证失败时显示错误提示

#### 操作流程
1. 用户切换到需求视图
2. 点击"需求上传"按钮
3. 在弹窗中选择需求分类（默认已选中当前分类）
4. 上传Excel格式的需求信息文件
5. 点击"确认上传"提交
6. 显示成功提示并关闭弹窗

### 6. 技术实现特点

#### 组件复用
- 复用新建用例弹窗的表单布局和样式
- 复用TreeSelect组件的渲染逻辑
- 复用Dragger上传组件的配置模式

#### 状态管理
- 统一的弹窗显示状态管理
- 分类树数据的缓存和复用
- 表单重置和清理逻辑

#### 错误处理
- 文件类型验证
- 文件大小限制
- 表单必填项验证
- 网络请求错误处理

### 7. 样式和布局

#### 弹窗样式
- 宽度: 600px
- 标题: "需求上传"
- 按钮: "确认上传" / "取消"
- CSS类名: `requirement-upload-modal`

#### 表单布局
- 使用与新建用例弹窗相同的formItemLayout
- 标签宽度: 6/24
- 内容宽度: 16/24

#### 上传区域
- 复用新建用例弹窗的dragger样式
- 文件图标: file-excel (Excel文件专用图标)
- 提示文本: "上传Excel文件（必传）"

### 8. 后续开发建议

#### 接口对接
1. 将Mock实现替换为真实的后端接口调用
2. 处理接口返回的错误信息和状态码
3. 添加上传进度显示功能

#### 功能增强
1. 支持需求信息模板下载
2. 添加文件内容预览功能
3. 支持批量需求上传
4. 添加上传历史记录

#### 用户体验优化
1. 添加上传进度条
2. 支持拖拽上传
3. 文件格式验证增强
4. 添加操作指引和帮助文档

所有功能已实现并可正常使用，用户可以在需求视图中体验完整的需求上传流程！
