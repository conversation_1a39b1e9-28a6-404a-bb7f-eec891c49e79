import React, { useEffect, useRef, useState } from 'react';
import { Spin, Alert, Button, Icon } from 'antd';
import './MindmapViewer.scss';

const MindmapViewer = ({ fileData, readonly = true, theme = 'fresh-blue' }) => {
  const containerRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [mindmapInstance, setMindmapInstance] = useState(null);

  useEffect(() => {
    if (!fileData) {
      setError('没有可显示的思维导图数据');
      setLoading(false);
      return;
    }

    initMindmap();
  }, [fileData, theme]);

  const initMindmap = async () => {
    try {
      setLoading(true);
      setError(null);

      // 模拟思维导图初始化
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 创建模拟的思维导图内容
      const mockMindmapData = {
        root: {
          data: { text: fileData.name || '根节点' },
          children: [
            {
              data: { text: '功能模块' },
              children: [
                { data: { text: '登录模块' } },
                { data: { text: '用户管理' } },
                { data: { text: '权限控制' } }
              ]
            },
            {
              data: { text: '测试用例' },
              children: [
                { data: { text: '正向测试' } },
                { data: { text: '负向测试' } },
                { data: { text: '边界测试' } }
              ]
            },
            {
              data: { text: '预期结果' },
              children: [
                { data: { text: '界面显示正确' } },
                { data: { text: '数据保存成功' } },
                { data: { text: '错误提示准确' } }
              ]
            }
          ]
        }
      };

      // 在这里可以集成真实的思维导图库，比如 kityminder 或其他
      renderMockMindmap(mockMindmapData);
      
      setLoading(false);
    } catch (err) {
      setError('思维导图加载失败：' + err.message);
      setLoading(false);
    }
  };

  const renderMockMindmap = (data) => {
    if (!containerRef.current) return;

    // 清空容器
    containerRef.current.innerHTML = '';

    // 创建 SVG 容器
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '100%');
    svg.setAttribute('viewBox', '0 0 800 600');
    svg.style.background = '#fafafa';

    // 绘制思维导图
    drawMindmapNode(svg, data.root, 400, 300, 0, true);

    containerRef.current.appendChild(svg);
  };

  const drawMindmapNode = (svg, node, x, y, level, isRoot = false) => {
    const colors = {
      'fresh-blue': ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399'],
      'default': ['#1890ff', '#52c41a', '#faad14', '#ff4d4f', '#722ed1']
    };
    
    const colorPalette = colors[theme] || colors['default'];
    const nodeColor = colorPalette[level % colorPalette.length];

    // 绘制节点
    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    rect.setAttribute('x', x - 40);
    rect.setAttribute('y', y - 15);
    rect.setAttribute('width', 80);
    rect.setAttribute('height', 30);
    rect.setAttribute('rx', 15);
    rect.setAttribute('fill', nodeColor);
    rect.setAttribute('opacity', isRoot ? '1' : '0.8');
    svg.appendChild(rect);

    // 绘制文本
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', x);
    text.setAttribute('y', y + 5);
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('fill', 'white');
    text.setAttribute('font-size', isRoot ? '12' : '10');
    text.setAttribute('font-weight', isRoot ? 'bold' : 'normal');
    text.textContent = node.data.text;
    svg.appendChild(text);

    // 绘制子节点
    if (node.children && node.children.length > 0) {
      const childCount = node.children.length;
      const angleStep = (2 * Math.PI) / childCount;
      const radius = isRoot ? 150 : 100;

      node.children.forEach((child, index) => {
        const angle = angleStep * index - Math.PI / 2;
        const childX = x + Math.cos(angle) * radius;
        const childY = y + Math.sin(angle) * radius;

        // 绘制连线
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', x);
        line.setAttribute('y1', y);
        line.setAttribute('x2', childX);
        line.setAttribute('y2', childY);
        line.setAttribute('stroke', nodeColor);
        line.setAttribute('stroke-width', '2');
        line.setAttribute('opacity', '0.6');
        svg.appendChild(line);

        // 递归绘制子节点
        drawMindmapNode(svg, child, childX, childY, level + 1);
      });
    }
  };

  const handleRefresh = () => {
    initMindmap();
  };

  const handleFullscreen = () => {
    if (containerRef.current) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      } else if (containerRef.current.webkitRequestFullscreen) {
        containerRef.current.webkitRequestFullscreen();
      } else if (containerRef.current.mozRequestFullScreen) {
        containerRef.current.mozRequestFullScreen();
      }
    }
  };

  return (
    <div className="mindmap-viewer">
      <div className="mindmap-toolbar">
        <div className="toolbar-left">
          <span className="mindmap-title">思维导图预览</span>
          {fileData && (
            <span className="file-info">
              {fileData.name} - {theme} 主题
            </span>
          )}
        </div>
        <div className="toolbar-right">
          <Button size="small" onClick={handleRefresh}>
            <Icon type="reload" />
            刷新
          </Button>
          <Button size="small" onClick={handleFullscreen}>
            <Icon type="fullscreen" />
            全屏
          </Button>
        </div>
      </div>

      <div className="mindmap-container" ref={containerRef}>
        {loading && (
          <div className="loading-container">
            <Spin size="large" />
            <p>正在加载思维导图...</p>
          </div>
        )}

        {error && (
          <div className="error-container">
            <Alert
              message="加载失败"
              description={error}
              type="error"
              showIcon
              action={
                <Button size="small" onClick={handleRefresh}>
                  重试
                </Button>
              }
            />
          </div>
        )}

        {!loading && !error && !fileData && (
          <div className="empty-container">
            <Icon type="file-image" style={{ fontSize: 64, color: '#d9d9d9' }} />
            <p>没有思维导图数据</p>
            <p className="sub-text">请先上传 XMind 文件</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MindmapViewer; 