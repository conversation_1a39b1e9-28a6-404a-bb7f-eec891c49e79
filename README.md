# 项目技术文档

## 项目简介
这是一个用例管理系统前端,主要用于管理和维护测试用例。

## 技术栈
- 前端框架: React 16.x
- UI组件库: Ant Design 3.23.6
- 路由: UmiJS
- 状态管理: -
- HTTP请求: Axios
- 工具库: 
  - Lodash
  - PropTypes
- 样式: SCSS

## 运行环境要求
- Node.js >= 12.x  // 12.1.0
- npm >= 6.x 或 yarn >= 1.22
- 现代浏览器(Chrome, Firefox, Safari, Edge等)

## 安装依赖
npm install

## 开发和打包说明

### 版本说明
- 在线版本:  `APP_VERSION=online` 部署到外网 使用的是外部的扫码登录鉴权服务
- 离线版本: `APP_VERSION=offline` 部署到内网 使用项目本身的登录和鉴权功能 

### 环境变量
项目通过 `APP_VERSION` 环境变量来区分在线版和离线版：
- online: 在线版本(默认)
- offline: 离线版本

### 开发模式
1. 在线版开发（默认）
npm start 或 npm run start:online

2. 离线版开发
npm run start:offline

### 生产环境打包
1. 在线版打包（默认）
npm run build 或 npm run build:online

2. 离线版打包
npm run build:offline

## 注意事项
1. 如果出现表格滚动样式问题，可能是antd版本问题导致，目前线上使用版本为3.23.6
2. 离线版和在线版的主要区别在于登录和鉴权服务的区别和WebSocket连接地址的配置
3. 离线版使用项目本身的登录和鉴权功能，在线版使用UmiJS的登录和鉴权功能
4. 离线版WebSocket连接地址为内网地址，在线版WebSocket连接地址为公网地址
5. 部署时请根据实际环境选择对应的打包版本
6. 环境变量 APP_VERSION 通过 cross-env 设置，确保在不同操作系统上都能正常工作