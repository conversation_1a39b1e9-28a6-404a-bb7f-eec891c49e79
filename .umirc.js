// ref: https://umijs.org/config/
const { NODE_ENV } = process.env;
const getRoutes = require('./src/routes');
const uglifyJSOptions =
  NODE_ENV === 'production'
    ? {
        uglifyOptions: {
          output: {
            comments: false, // remove comments
          },
          compress: {
            unused: true,
            dead_code: true, // big one--strip code that will never execute
            warnings: false, // good for prod apps so users can't peek behind curtain
            drop_debugger: true,
            conditionals: true,
            evaluate: true,
            drop_console: true, // strips console statements
            sequences: true,
            booleans: true,
          },
        },
        sourceMap: false,
        parallel: true,
      }
    : {};

const proxy =
  NODE_ENV !== 'production'
    ? {
        '/api': {
          // target: `http://localhost:8094`, // 测试环境
          target: `http://**************:8443/thirdapp/ecocase`, // 开发环境
          changeOrigin: true,
          // pathRewrite: { '^/api': '' },
        },
      }
    : {};

const chainWebpack = config => {
  if (NODE_ENV === 'production') {
  //   config.output
  //   .filename('assets/js/[name].[hash:8].js')
  //   .chunkFilename('assets/js/[name].[chunkhash:8].async.js');

  // // 修改 css 输出路径
  // config.plugin('extract-css')
  //   .tap(args => [{
  //     ...args[0],
  //     filename: 'assets/css/[name].[contenthash:8].css',
  //     chunkFilename: 'assets/css/[name].[contenthash:8].chunk.css',
  //   }]);

  // // 优化分包配置
  // config.optimization.splitChunks({
  //   chunks: 'all',
  //   automaticNameDelimiter: '.',
  //   cacheGroups: {
  //     vendor: {
  //       name: 'vendors',
  //       test: /[\\/]node_modules[\\/]/,
  //       priority: 10,
  //       chunks: 'all'
  //     },
  //     commons: {
  //       name: 'commons',
  //       minChunks: 2,
  //       priority: 0,
  //       chunks: 'all'
  //     }
  //   }
  // });
    config.merge({
      optimization: {
        minimize: true,
        splitChunks: {
          chunks: 'async',
          minSize: 30000,
          minChunks: 2,
          automaticNameDelimiter: '.',
          cacheGroups: {
            vendor: {
              name: 'vendors',
              test: /^.*node_modules[\\/](?!ag-grid-|d3-|dva|braft-editor|bizcharts|lodash|react-virtualized|rc-select|rc-drawer|rc-time-picker|rc-tree|rc-table|rc-calendar|antd).*$/,
              chunks: 'all',
              priority: 10,
            },
            virtualized: {
              name: 'virtualized',
              test: /[\\/]node_modules[\\/]react-virtualized/,
              chunks: 'all',
              priority: 10,
            },
            rcselect: {
              name: 'rc-select',
              test: /[\\/]node_modules[\\/]rc-select/,
              chunks: 'all',
              priority: 10,
            },
            rcdrawer: {
              name: 'rcdrawer',
              test: /[\\/]node_modules[\\/]rc-drawer/,
              chunks: 'all',
              priority: 10,
            },
            rctimepicker: {
              name: 'rctimepicker',
              test: /[\\/]node_modules[\\/]rc-time-picker/,
              chunks: 'all',
              priority: 10,
            },
            ag: {
              name: 'ag',
              test: /[\\/]node_modules[\\/]ag-grid-/,
              chunks: 'all',
              priority: 10,
            },
            antd: {
              name: 'antd',
              test: /[\\/]node_modules[\\/]antd[\\/]/,
              chunks: 'all',
              priority: 9,
            },
            dva: {
              name: 'dva',
              test: /[\\/]node_modules[\\/]dva/,
              chunks: 'all',
              priority: 10,
            },
            rctree: {
              name: 'rctree',
              test: /[\\/]node_modules[\\/]rc-tree/,
              chunks: 'all',
              priority: -1,
            },
            rccalendar: {
              name: 'rccalendar',
              test: /[\\/]node_modules[\\/]rc-calendar[\\/]/,
              chunks: 'all',
              priority: -1,
            },
            rctable: {
              name: 'rctable',
              test: /[\\/]node_modules[\\/]rc-table[\\/]es[\\/]/,
              chunks: 'all',
              priority: -1,
            },
            lodash: {
              name: 'lodash',
              test: /[\\/]node_modules[\\/]lodash[\\/]/,
              chunks: 'all',
              priority: -2,
            },
          },
        },
      },
    });
    //过滤掉momnet的那些不使用的国际化文件
    config
      .plugin('replace')
      .use(require('webpack').ContextReplacementPlugin)
      .tap(() => {
        return [/moment[/\\]locale$/, /zh-cn/];
      });
  }
  return config;
};
export default {
  // base: '/thirdapp/ecocase/',
  // base: NODE_ENV === 'production' ? '/thirdapp/ecocase/' : '/',
  // publicPath: NODE_ENV === 'production' ? '/thirdapp/ecocase/' : '/',
  base: '/thirdapp/ecocase/',
  publicPath: '/thirdapp/ecocase/', 
  hash: true,
  history: 'hash',
  treeShaking: true,
  cssLoaderOptions: {
    localIdentName: '[local]',
  },
  chainWebpack,
  proxy,
  uglifyJSOptions,
  plugins: [
    // ref: https://umijs.org/plugin/umi-plugin-react.html
    [
      'umi-plugin-react',
      {
        antd: true,
        dva: true,
        dynamicImport: { webpackChunkName: true },
        title: 'agiletc-web',
        dll: true,
        routes: {
          exclude: [
            /models\//,
            /services\//,
            /model\.(t|j)sx?$/,
            /service\.(t|j)sx?$/,
            /components\//,
          ],
        },
      },
    ],
  ],
  disableRedirectHoist: true,
  // routes: [
  //   {
  //     exact: true,  // 添加 exact 确保精确匹配
  //     path: '/caseManager/:productLineId/:caseId/:itemid/:iscore',
  //     component: './testTask/index.js',
  //     title:'用例详情'
  //   },
  //   {
  //     exact: true,  // 添加 exact 确保精确匹配
  //     path: '/caseManager/historyContrast/:caseId1/:caseId2',
  //     component: './contrast/seeResult.js',
  //     title:'版本比较'
  //   },
  //   {
  //     path: '/',
  //     component: '../layouts/BasicLayout/index.js',
  //     routes: [
  //       // {
  //       //   exact: true,
  //       //   path: '/',
  //       //   component: './landing/index.jsx',
  //       // },
  //       {
  //         exact: true,
  //         path: '/',
  //         component: './landing/newIndex.jsx',
  //         title: '首页'
  //       }, 
  //       {
  //         path: '/admin',
  //         routes: [
  //           {
  //             path: '/admin/users',
  //             component: './admin/users/index.jsx',
  //             exact: true,
  //             title: '用户管理',
  //           },
  //           {
  //             path: '/admin/project',
  //             component: './admin/project/index.jsx',
  //             exact: true,
  //             title: '项目管理',
  //           },
  //         ],
  //       },
  //       {
  //         path: '/case/caseList/:productLineId',
  //         component: './casepage/index.js',
  //         title:'用例管理'
  //       },
  //       // {
  //       //   path: '/caseManager/:productLineId/:caseId/:itemid/:iscore',
  //       //   component: './testTask/index.js',
  //       //   title:'用例详情'
  //       // },
  //       // {
  //       //   path: '/login',
  //       //   component: './landing/login.jsx',
  //       // },
  //       {
  //         path: '/history/:caseId',
  //         component: './contrast/index.jsx',
  //       },
  //       // {
  //       //   path: '/caseManager/historyContrast/:caseId1/:caseId2',
  //       //   component: './contrast/seeResult.js',
  //       // },
  //       {
  //         path: '*',
  //         redirect: '/',
  //       },
  //     ],
      
  //   }, 
  // ],
  routes: getRoutes,
  define: {
    'process.env.APP_VERSION': process.env.APP_VERSION,
  }
};
